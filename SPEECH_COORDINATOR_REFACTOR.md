# 🎯 Speech Coordinator Refactor - Solución de Solapamientos

## 📋 Resumen del Problema

### Problemas Identificados:
1. **Múltiples sistemas de speech funcionando en paralelo** sin coordinación
2. **Solapamientos de audio** - varios speech reproduciéndose simultáneamente
3. **Falta de criterios de priorización** - no hay reglas claras sobre qué speech tiene prioridad
4. **Doble reproducción** - SpeechService y AudioManager reproduciendo el mismo audio
5. **Conflictos MHC vs Web** - hardware y web speech funcionando independientemente

### Sistemas Conflictivos Originales:
- `SpeechService` (Azure TTS) - con su propio elemento `<audio>`
- `AudioManager` - con elementos `<audio>` separados
- `MHC speakAura` - sistema de hardware independiente
- `SpeechOutputContext` - capa de abstracción sin coordinación real

## 🔧 Solución Implementada

### 1. **SpeechCoordinator** - Sistema Centralizado
**Archivo:** `src/services/SpeechCoordinator.ts`

**Características:**
- ✅ **Cola de prioridades** - maneja múltiples speech según importancia
- ✅ **Prevención de solapamientos** - solo un speech activo a la vez
- ✅ **Coordinación Hardware vs Web** - decide automáticamente el mejor canal
- ✅ **Interrupciones inteligentes** - speech de mayor prioridad interrumpe al menor
- ✅ **Gestión de timeouts** - evita speech colgados
- ✅ **Estado reactivo** - notifica cambios en tiempo real

**Niveles de Prioridad:**
```typescript
CRÍTICO (4): error, validation
ALTO (3):    game_response, victory, instruction  
MEDIO (2):   question, welcome, hint
BAJO (1):    info, effect
```

### 2. **Hook Reactivo** - useSpeechCoordinator
**Archivo:** `src/hooks/useSpeechCoordinator.ts`

**Características:**
- ✅ **Estado en tiempo real** - se actualiza automáticamente
- ✅ **Métodos de conveniencia** - funciones específicas por tipo
- ✅ **Hooks especializados** - `useGameSpeech()`, `useSimpleSpeech()`
- ✅ **Control granular** - interrupt, clearQueue, stopAll

### 3. **Refactorización Completa**

**Archivos Modificados:**
- ✅ `src/contexts/SpeechOutputContext.tsx` - usa coordinador internamente
- ✅ `src/contexts/GameOrchestratorContext.tsx` - migrado a `useGameSpeech()`
- ✅ `src/App.tsx` - usa coordinador para bienvenidas
- ✅ `src/components/WelcomeScreen.tsx` - migrado al coordinador
- ✅ `src/components/Debug/AudioDebugPanel.tsx` - usa coordinador

**Cambios Principales:**
```typescript
// ANTES (problemático)
await speechOutput.speakGameMessage(text, "system");
await speechService.speak(text);
mhc.speakAura(text); // Sin coordinación

// DESPUÉS (coordinado)
await gameSpeech.speakWelcome(text);
await speechCoordinator.speak(text, "welcome");
```

## 🧪 Sistema de Pruebas

### 1. **Componente de Prueba Visual**
**Archivo:** `src/components/Debug/SpeechCoordinatorTest.tsx`

**Funcionalidades:**
- 🎮 **Pruebas por tipo** - verifica prioridades
- 📡 **Pruebas por canal** - MHC vs Web
- 🧪 **Pruebas de solapamiento** - múltiples speech simultáneos
- 📊 **Estado en tiempo real** - visualización del coordinador

### 2. **Tests Automatizados**
**Archivo:** `src/utils/speechCoordinatorTests.ts`

**Pruebas Incluidas:**
- ✅ `testBasicSpeech()` - funcionamiento básico
- ✅ `testPriorities()` - sistema de prioridades
- ✅ `testQueue()` - gestión de cola
- ✅ `testInterruption()` - interrupciones
- ✅ `testConvenienceMethods()` - métodos de conveniencia
- ✅ `testOverlapPrevention()` - prevención de solapamientos

**Uso:**
```typescript
import { runSpeechCoordinatorTests, quickOverlapTest } from './utils/speechCoordinatorTests';

// Prueba completa
const results = await runSpeechCoordinatorTests();

// Prueba rápida
const noOverlaps = await quickOverlapTest();
```

## 📊 Beneficios Obtenidos

### ✅ **Problemas Resueltos:**
1. **Sin solapamientos** - solo un speech activo a la vez
2. **Prioridades claras** - errores interrumpen info, respuestas tienen prioridad sobre preguntas
3. **Coordinación MHC/Web** - decisión automática del mejor canal
4. **Cola inteligente** - mensajes se procesan en orden de prioridad
5. **Estado consistente** - toda la app conoce el estado del speech

### ✅ **Mejoras de Rendimiento:**
- **Menos llamadas API** - evita requests duplicados a Azure TTS
- **Mejor UX** - no hay audio superpuesto confuso
- **Gestión de memoria** - limpieza automática de URLs de audio
- **Fallbacks robustos** - si MHC falla, usa web automáticamente

### ✅ **Mantenibilidad:**
- **Código centralizado** - toda la lógica de speech en un lugar
- **Tipado fuerte** - TypeScript previene errores
- **Hooks reutilizables** - fácil de usar en cualquier componente
- **Tests automatizados** - verificación continua del funcionamiento

## 🚀 Cómo Usar

### **Para Componentes del Juego:**
```typescript
import { useGameSpeech } from '../hooks/useSpeechCoordinator';

const MyComponent = () => {
  const { speakResponse, speakQuestion, speakValidation } = useGameSpeech();
  
  // Respuesta de la IA (alta prioridad)
  await speakResponse("Sí, el personaje es masculino");
  
  // Pregunta al usuario (prioridad media)
  await speakQuestion("¿El personaje es de una película?");
  
  // Error de validación (prioridad crítica)
  await speakValidation("No entendí esa respuesta");
};
```

### **Para Casos Simples:**
```typescript
import { useSimpleSpeech } from '../hooks/useSpeechCoordinator';

const MyComponent = () => {
  const { speak, speakError, isSpeaking, stopAll } = useSimpleSpeech();
  
  await speak("Mensaje general");
  await speakError("¡Error crítico!");
  
  if (isSpeaking) {
    stopAll();
  }
};
```

### **Para Control Avanzado:**
```typescript
import { useSpeechCoordinator } from '../hooks/useSpeechCoordinator';

const MyComponent = () => {
  const { 
    speak, 
    interrupt, 
    clearQueue, 
    state,
    speakMHC,
    speakWeb 
  } = useSpeechCoordinator();
  
  // Speech con opciones avanzadas
  await speak("Mensaje importante", "instruction", {
    priority: "high",
    channel: "mhc",
    onComplete: () => console.log("Terminado!"),
    onError: (error) => console.error(error)
  });
  
  // Forzar canal específico
  await speakMHC("Solo por hardware");
  await speakWeb("Solo por web");
  
  // Control de cola
  interrupt(); // Interrumpe speech actual
  clearQueue(); // Limpia cola pendiente
};
```

## 🔍 Verificación

### **Comprobar que funciona:**
1. **Abrir DevTools** y ir a Console
2. **Ejecutar prueba rápida:**
   ```javascript
   import('./utils/speechCoordinatorTests').then(tests => tests.quickOverlapTest());
   ```
3. **Verificar logs** - debería mostrar "✅ Sin solapamientos"

### **Panel de Debug:**
1. **Añadir componente de prueba** a cualquier vista:
   ```typescript
   import { SpeechCoordinatorTest } from './components/Debug/SpeechCoordinatorTest';
   
   // En tu componente
   <SpeechCoordinatorTest />
   ```
2. **Probar diferentes tipos** de speech
3. **Verificar que no se solapan** y que las prioridades funcionan

## 📝 Notas Importantes

### **Compatibilidad:**
- ✅ **Retrocompatible** - `SpeechOutputContext` sigue funcionando
- ✅ **Migración gradual** - se puede migrar componente por componente
- ✅ **Fallbacks** - si el coordinador falla, usa el sistema anterior

### **Configuración:**
- **MHC Detection** - detecta automáticamente si hardware está disponible
- **Azure TTS** - usa la configuración existente de `SpeechService`
- **Audio Manager** - aprovecha el ducking y gestión de volumen existente

### **Limitaciones:**
- **MHC Feedback** - no hay forma de saber cuándo termina el speech de hardware
- **Estimación de duración** - usa heurísticas basadas en longitud del texto
- **Dependencias** - requiere que `SpeechService` y `AudioManager` estén configurados

---

**🎉 Resultado:** Sistema de speech robusto, sin solapamientos, con prioridades claras y fácil de usar.
