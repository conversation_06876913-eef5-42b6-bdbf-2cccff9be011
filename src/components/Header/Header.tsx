import { useState } from "react";
import { useSpeechOutput } from "../../contexts/SpeechOutputContext";
import IconGoBack from "../icons/GoBack/IconGoBack";
import IconHome from "../icons/Home/IconHome";
import IconMenu from "../icons/Menu/IconMenu";
import IconSoundOn from "../icons/SoundOn/IconSoundOn";
import "./Header.scss";

// CSS adicional para los controles de audio
const additionalStyles = `
.header .sound-icon {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 4px;
  border-radius: 6px;
}

.header .sound-icon:hover {
  background: rgba(136, 255, 213, 0.1);
  transform: scale(1.1);
}

.header .sound-icon.muted {
  opacity: 0.6;
}

.header .sound-icon.active {
  background: rgba(136, 255, 213, 0.15);
}

.header .audio-indicator {
  position: absolute;
  top: -4px;
  right: -4px;
  display: flex;
  gap: 2px;
}

.header .music-indicator,
.header .speech-indicator {
  background: #88FFD5;
  color: #0b2739;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 8px;
  font-weight: bold;
}

.audio-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.audio-menu {
  background: #0b2739;
  border: 2px solid #88FFD5;
  border-radius: 12px;
  padding: 24px;
  min-width: 300px;
  max-width: 400px;
}

.audio-menu h3 {
  margin: 0 0 20px 0;
  color: #88FFD5;
  text-align: center;
}

.audio-controls .control-group {
  margin-bottom: 16px;
}

.audio-controls label {
  display: block;
  color: #88FFD5;
  font-size: 14px;
  margin-bottom: 6px;
  font-weight: 600;
}

.control-row {
  display: flex;
  align-items: center;
  gap: 10px;
}

.control-row input[type="range"] {
  flex: 1;
  height: 4px;
  background: rgba(136, 255, 213, 0.2);
  border-radius: 2px;
  outline: none;
  -webkit-appearance: none;
}

.control-row input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #88FFD5;
  cursor: pointer;
}

.control-row input[type="range"]::-moz-range-thumb {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: #88FFD5;
  cursor: pointer;
  border: none;
}

.control-row span {
  color: white;
  font-size: 12px;
  min-width: 35px;
  text-align: right;
}

.status {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  margin-top: 4px;
}

.audio-actions {
  display: flex;
  gap: 12px;
  margin-top: 20px;
}

.audio-actions button {
  flex: 1;
  padding: 10px 16px;
  border: 1px solid #88FFD5;
  border-radius: 6px;
  background: transparent;
  color: #88FFD5;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
}

.audio-actions button:hover {
  background: rgba(136, 255, 213, 0.1);
}

.audio-actions button.mute {
  background: rgba(255, 100, 100, 0.2);
  border-color: #ff6464;
  color: #ff6464;
}

.audio-actions button.unmute {
  background: rgba(136, 255, 213, 0.2);
  color: #88FFD5;
}
`;

// Inyectar estilos
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = additionalStyles;
  document.head.appendChild(styleElement);
}

interface HeaderProps {
  currentView: string;
  onBackToMain?: () => void;
  onToggleSound?: () => void;
  onGoHome?: () => void;
  showBackButton?: boolean;
}

// Icono de sonido apagado
const IconSoundOff: React.FC = () => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.6616 5.15217C17.3328 4.95376 16.9337 4.95002 16.5975 5.13719L6.98687 10.5915H4.06045C3.47665 10.5915 3 11.0745 3 11.6659V19.3365C3 19.928 3.47665 20.4109 4.06045 20.4109H6.98687L16.5679 25.8465C16.7379 25.9476 16.9263 26 17.1185 26C17.3106 26 17.4769 25.9551 17.6395 25.8615C17.9794 25.6706 18.19 25.3074 18.19 24.9106V6.08805C18.19 5.69873 17.9942 5.35058 17.6616 5.15217Z"
      fill="white"
      opacity="0.5"
    />
    {/* Línea de "mute" */}
    <line x1="22" y1="8" x2="28" y2="24" stroke="white" strokeWidth="2" strokeLinecap="round"/>
    <line x1="28" y1="8" x2="22" y2="24" stroke="white" strokeWidth="2" strokeLinecap="round"/>
  </svg>
);

export const Header: React.FC<HeaderProps> = ({
  currentView,
  onBackToMain,
  onGoHome,
  showBackButton = false,
}) => {
  const {
    state: { audioState, isMusicPlaying, isSpeechPlaying },
    toggleMute,
    pauseAll,
    resumeAll
  } = useSpeechOutput();

  const [showAudioMenu, setShowAudioMenu] = useState(false);

  const renderTitle = () => {
    switch (currentView) {
      case "play":
        return "Enygma";
      case "rules":
        return "Reglas";
      case "lives":
        return "Tus preguntas restantes";
      case "clues":
        return "Pistas descubiertas";
      default:
        return "";
    }
  };

  const handleSoundClick = () => {
    // Si hay audio reproduciéndose, pausar/reanudar
    if (isMusicPlaying || isSpeechPlaying) {
      if (audioState.isMuted) {
        toggleMute();
        resumeAll();
      } else {
        pauseAll();
      }
    } else {
      // Si no hay audio, solo toggle mute
      toggleMute();
    }
  };

  const handleLongPress = () => {
    // Mostrar menú avanzado de audio
    setShowAudioMenu(true);
  };

  const isAudioActive = isMusicPlaying || isSpeechPlaying;
  const showMutedIcon = audioState.isMuted || (!isAudioActive && audioState.isMuted);

  return (
    <>
      <div className="header">
        <div className="header-left">
          {currentView === "main" && (
            <IconMenu />
          )}

          {showBackButton && currentView !== "main" && onBackToMain && (
            <div className="back-button" onClick={onBackToMain}>
              <IconGoBack />
            </div>
          )}
        </div>

        <div className="header-title">
          {renderTitle()}
        </div>

        <div className="header-right">
          <div
            className={`sound-icon ${showMutedIcon ? 'muted' : ''} ${isAudioActive ? 'active' : ''}`}
            onClick={handleSoundClick}
            onMouseDown={(e) => {
              // Detectar long press para menú avanzado
              const timer = setTimeout(handleLongPress, 1000);

              const handleMouseUp = () => {
                clearTimeout(timer);
                document.removeEventListener('mouseup', handleMouseUp);
              };

              document.addEventListener('mouseup', handleMouseUp);
            }}
            title={
              audioState.isMuted
                ? "Audio silenciado - Click para activar"
                : isAudioActive
                  ? "Audio reproduciéndose - Click para pausar"
                  : "Click para silenciar/activar audio"
            }
          >
            {showMutedIcon ? <IconSoundOff /> : <IconSoundOn />}

            {/* Indicador de actividad de audio */}
            {isAudioActive && !audioState.isMuted && (
              <div className="audio-indicator">
                {isMusicPlaying && <div className="music-indicator">♪</div>}
                {isSpeechPlaying && <div className="speech-indicator">💬</div>}
              </div>
            )}
          </div>

          <div className="home-icon" onClick={onGoHome}>
            <IconHome />
          </div>
        </div>
      </div>

      {/* Menú avanzado de audio */}
      {showAudioMenu && (
        <div className="audio-menu-overlay" onClick={() => setShowAudioMenu(false)}>
          <div className="audio-menu" onClick={(e) => e.stopPropagation()}>
            <h3>Control de Audio</h3>

            <div className="audio-controls">
              <div className="control-group">
                <label>Música de fondo</label>
                <div className="control-row">
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={audioState.music.volume}
                    onChange={(e) => {
                      // Aquí conectarías con setMusicVolume del contexto
                      console.log("Music volume:", e.target.value);
                    }}
                  />
                  <span>{Math.round(audioState.music.volume * 100)}%</span>
                </div>
                <div className="status">
                  {isMusicPlaying ? "🎵 Reproduciéndose" : "⏸️ Pausado"}
                </div>
              </div>

              <div className="control-group">
                <label>Narración</label>
                <div className="control-row">
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={audioState.speech.volume}
                    onChange={(e) => {
                      // Aquí conectarías con setSpeechVolume del contexto
                      console.log("Speech volume:", e.target.value);
                    }}
                  />
                  <span>{Math.round(audioState.speech.volume * 100)}%</span>
                </div>
                <div className="status">
                  {isSpeechPlaying ? "🗣️ Hablando" : "🔇 Silencio"}
                </div>
              </div>

              <div className="control-group">
                <label>Volumen General</label>
                <div className="control-row">
                  <input
                    type="range"
                    min="0"
                    max="1"
                    step="0.1"
                    value={audioState.masterVolume}
                    onChange={(e) => {
                      // Aquí conectarías con setMasterVolume del AudioManager
                      console.log("Master volume:", e.target.value);
                    }}
                  />
                  <span>{Math.round(audioState.masterVolume * 100)}%</span>
                </div>
              </div>
            </div>

            <div className="audio-actions">
              <button
                onClick={() => {
                  toggleMute();
                  setShowAudioMenu(false);
                }}
                className={audioState.isMuted ? "unmute" : "mute"}
              >
                {audioState.isMuted ? "🔊 Activar Audio" : "🔇 Silenciar Todo"}
              </button>

              <button onClick={() => setShowAudioMenu(false)}>
                Cerrar
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};
