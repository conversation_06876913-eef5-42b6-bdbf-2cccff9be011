// ========== ANIMATIONS ==========
@keyframes modalFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(12px);
  }
}

@keyframes pageFlipNext {
  0% {
    transform: perspective(1000px) rotateY(0deg);
    opacity: 1;
  }
  50% {
    transform: perspective(1000px) rotateY(-90deg);
    opacity: 0.3;
  }
  100% {
    transform: perspective(1000px) rotateY(0deg);
    opacity: 1;
  }
}

@keyframes pageFlipPrev {
  0% {
    transform: perspective(1000px) rotateY(0deg);
    opacity: 1;
  }
  50% {
    transform: perspective(1000px) rotateY(90deg);
    opacity: 0.3;
  }
  100% {
    transform: perspective(1000px) rotateY(0deg);
    opacity: 1;
  }
}

.rules-modal {
  flex: auto !important;
  flex-direction: column;
  justify-content: center;
}

// ========== MAIN CONTAINER ==========
.rules-container {
  display: flex;
  gap: 20px;
  max-width: 900px;
  width: 90%;

  @media (max-width: 768px) {
    flex-direction: column;
    width: 95%;
    gap: 15px;
  }
}

// ========== IMAGE CARD ==========
.rules-image-card {
  flex: 0 0 45%;
  background: linear-gradient(135deg, #002332 0%, #001a26 100%);
  border: 2px solid #88ffd5;
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  @media (max-width: 768px) {
    min-height: 250px;

    &:empty::after {
      font-size: 60px;
    }
  }
}

// ========== TEXT CARD ==========
.rules-text-card {
  flex: 1;
  background: linear-gradient(135deg, #002332 0%, #001a26 100%);
  border: 2px solid #88ffd5;
  border-radius: 16px;
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 400px;
  position: relative;

  h3 {
    color: #4ade80;
    margin: 0 0 30px 0;
    font-family: "Open Sans", sans-serif;
    font-size: 1.5rem;
    font-weight: 600;
    text-align: center;
    line-height: 1.3;
    display: flex;
    align-items: center;
    height: 100%;
  }

  // Indicadores de página - DENTRO de la card
  .rules-page-indicators {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-top: 30px;

    .rules-page-indicator {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: rgba(74, 222, 128, 0.3);
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        background: #4ade80;
        transform: scale(1.5);
      }

      &:hover:not(.active) {
        background: rgba(74, 222, 128, 0.6);
        transform: scale(1.2);
      }
    }
  }

  @media (max-width: 768px) {
    padding: 30px 25px;
    min-height: 350px;

    h3 {
      font-size: 1.3rem;
      margin-bottom: 25px;
    }

    .rules-page-indicators {
      margin-top: 25px;
    }
  }
}

// ========== NAVIGATION ARROWS - FUERA de las cards ==========
.rules-nav-arrow {
  position: fixed;
  top: 55%;
  transform: translateY(-50%);
  background: rgba(11, 39, 57, 0.9);
  border: 2px solid #88ffd5;
  color: #88ffd5;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 1.8rem;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  z-index: 1001;

  &:hover:not(.disabled) {
    background: rgba(11, 39, 57, 1);
    border-color: #4ade80;
    color: #4ade80;
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 0 15px rgba(136, 255, 213, 0.3);
  }

  &.disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }

  &.prev {
    left: calc(49% - 500px);
  }

  &.next {
    right: calc(49% - 500px);
  }

  @media (max-width: 1200px) {
    &.prev {
      left: 20px;
    }

    &.next {
      right: 20px;
    }
  }

  @media (max-width: 768px) {
    display: none; // Ocultar flechas en móvil, solo usar indicadores
  }
}

// ========== PAGE FLIP ANIMATIONS ==========
.rules-page-container {
  // perspective: 1200px;
  width: 100%;
  display: flex;
  justify-content: center;

  &.page-flip-next {
    // .rules-image-card,
    .rules-text-card {
      animation: pageFlipNext 0.6s ease-in-out;
    }
  }

  &.page-flip-prev {
    // .rules-image-card,
    .rules-text-card {
      animation: pageFlipPrev 0.6s ease-in-out;
    }
  }
}

// ========== RESPONSIVE ADJUSTMENTS ==========
@media (max-width: 480px) {
  .rules-container {
    width: 98%;
  }

  .rules-close-button {
    top: 15px;
    right: 15px;
    padding: 6px 10px;
    font-size: 1.1rem;
  }

  .rules-text-card {
    padding: 25px 20px;
    min-height: 320px;

    h3 {
      font-size: 1.2rem;
    }
  }

  .rules-image-card {
    min-height: 200px;
  }
}
