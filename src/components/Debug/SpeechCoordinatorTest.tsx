/**
 * ========================================================================
 * COMPONENTE DE PRUEBA: SpeechCoordinatorTest
 * ========================================================================
 *
 * Componente para probar y validar el funcionamiento del coordinador de speech
 * Permite verificar que no hay solapamientos y que las prioridades funcionan
 * ========================================================================
 */

import React, { useState } from "react";
import { useSpeechCoordinator } from "../../hooks/useSpeechCoordinator";

export const SpeechCoordinatorTest: React.FC = () => {
  const {
    state,
    isSpeaking,
    queueLength,
    currentSpeech,
    speak,
    speakError,
    speakValidation,
    speakGameResponse,
    speakGameQuestion,
    speakWelcome,
    speakHint,
    speakInfo,
    interrupt,
    clearQueue,
    stopAll,
    speakMHC,
    speakWeb,
  } = useSpeechCoordinator();

  const [testText, setTestText] = useState("Texto de prueba para el coordinador");

  // ========== HANDLERS DE PRUEBA ==========
  const handleTestBasic = async () => {
    await speak(testText);
  };

  const handleTestError = async () => {
    await speakError("Este es un mensaje de error crítico");
  };

  const handleTestValidation = async () => {
    await speakValidation("No entendí esa respuesta, inténtalo de nuevo");
  };

  const handleTestGameResponse = async () => {
    await speakGameResponse("Sí, el personaje es masculino");
  };

  const handleTestGameQuestion = async () => {
    await speakGameQuestion("¿El personaje es de una película?");
  };

  const handleTestWelcome = async () => {
    // await speakWelcome("¡Bienvenido a Enygma!");
  };

  const handleTestHint = async () => {
    await speakHint("Te doy una pista: piensa en superhéroes");
  };

  const handleTestInfo = async () => {
    await speakInfo("Esta es información general del juego");
  };

  const handleTestMHC = async () => {
    await speakMHC("Probando speech por hardware MHC");
  };

  const handleTestWeb = async () => {
    await speakWeb("Probando speech por web audio");
  };

  const handleTestOverlap = async () => {
    // Probar múltiples speech simultáneos para verificar coordinación
    console.log("🧪 Probando solapamientos...");

    // Lanzar múltiples speech de diferentes prioridades
    speakInfo("Mensaje de baja prioridad 1");
    speakGameQuestion("Pregunta de prioridad media");
    speakError("¡Error crítico!"); // Debería interrumpir
    speakInfo("Mensaje de baja prioridad 2");
    speakGameResponse("Respuesta de alta prioridad");
  };

  const handleTestQueue = async () => {
    console.log("🧪 Probando cola de mensajes...");

    // Añadir varios mensajes de la misma prioridad
    for (let i = 1; i <= 5; i++) {
      speakInfo(`Mensaje en cola número ${i}`);
    }
  };

  // ========== RENDER ==========
  return (
    <div style={{
      padding: "20px",
      border: "2px solid #007bff",
      borderRadius: "8px",
      margin: "20px",
      backgroundColor: "#f8f9fa"
    }}>
      <h3>🎯 Speech Coordinator Test Panel</h3>

      {/* Estado actual */}
      <div style={{ marginBottom: "20px", padding: "10px", backgroundColor: "#e9ecef", borderRadius: "4px" }}>
        <h4>📊 Estado Actual</h4>
        <p><strong>Hablando:</strong> {isSpeaking ? "✅ Sí" : "❌ No"}</p>
        <p><strong>Cola:</strong> {queueLength} mensajes</p>
        <p><strong>Canal:</strong> {state.channel}</p>
        <p><strong>Speech actual:</strong> {currentSpeech ? `"${currentSpeech.substring(0, 50)}..."` : "Ninguno"}</p>
        <p><strong>Última actividad:</strong> {new Date(state.lastActivity).toLocaleTimeString()}</p>
      </div>

      {/* Input de texto personalizado */}
      <div style={{ marginBottom: "20px" }}>
        <label>
          <strong>Texto de prueba:</strong>
          <br />
          <input
            type="text"
            value={testText}
            onChange={(e) => setTestText(e.target.value)}
            style={{ width: "100%", padding: "8px", marginTop: "5px" }}
            placeholder="Escribe un texto para probar..."
          />
        </label>
      </div>

      {/* Botones de prueba por tipo */}
      <div style={{ marginBottom: "20px" }}>
        <h4>🎮 Pruebas por Tipo de Speech</h4>
        <div style={{ display: "grid", gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))", gap: "10px" }}>
          <button onClick={handleTestBasic} style={{ padding: "8px", backgroundColor: "#6c757d", color: "white", border: "none", borderRadius: "4px" }}>
            Básico (Info)
          </button>
          <button onClick={handleTestError} style={{ padding: "8px", backgroundColor: "#dc3545", color: "white", border: "none", borderRadius: "4px" }}>
            Error (Crítico)
          </button>
          <button onClick={handleTestValidation} style={{ padding: "8px", backgroundColor: "#fd7e14", color: "white", border: "none", borderRadius: "4px" }}>
            Validación (Crítico)
          </button>
          <button onClick={handleTestGameResponse} style={{ padding: "8px", backgroundColor: "#198754", color: "white", border: "none", borderRadius: "4px" }}>
            Respuesta (Alto)
          </button>
          <button onClick={handleTestGameQuestion} style={{ padding: "8px", backgroundColor: "#0d6efd", color: "white", border: "none", borderRadius: "4px" }}>
            Pregunta (Medio)
          </button>
          <button onClick={handleTestWelcome} style={{ padding: "8px", backgroundColor: "#6f42c1", color: "white", border: "none", borderRadius: "4px" }}>
            Bienvenida (Medio)
          </button>
          <button onClick={handleTestHint} style={{ padding: "8px", backgroundColor: "#20c997", color: "white", border: "none", borderRadius: "4px" }}>
            Pista (Medio)
          </button>
          <button onClick={handleTestInfo} style={{ padding: "8px", backgroundColor: "#adb5bd", color: "white", border: "none", borderRadius: "4px" }}>
            Info (Bajo)
          </button>
        </div>
      </div>

      {/* Botones de prueba por canal */}
      <div style={{ marginBottom: "20px" }}>
        <h4>📡 Pruebas por Canal</h4>
        <div style={{ display: "flex", gap: "10px" }}>
          <button onClick={handleTestMHC} style={{ padding: "8px", backgroundColor: "#e83e8c", color: "white", border: "none", borderRadius: "4px" }}>
            MHC (Hardware)
          </button>
          <button onClick={handleTestWeb} style={{ padding: "8px", backgroundColor: "#17a2b8", color: "white", border: "none", borderRadius: "4px" }}>
            Web Audio
          </button>
        </div>
      </div>

      {/* Pruebas avanzadas */}
      <div style={{ marginBottom: "20px" }}>
        <h4>🧪 Pruebas Avanzadas</h4>
        <div style={{ display: "flex", gap: "10px" }}>
          <button onClick={handleTestOverlap} style={{ padding: "8px", backgroundColor: "#ffc107", color: "black", border: "none", borderRadius: "4px" }}>
            Probar Solapamientos
          </button>
          <button onClick={handleTestQueue} style={{ padding: "8px", backgroundColor: "#6610f2", color: "white", border: "none", borderRadius: "4px" }}>
            Probar Cola
          </button>
        </div>
      </div>

      {/* Controles */}
      <div>
        <h4>🎛️ Controles</h4>
        <div style={{ display: "flex", gap: "10px" }}>
          <button onClick={interrupt} style={{ padding: "8px", backgroundColor: "#dc3545", color: "white", border: "none", borderRadius: "4px" }}>
            Interrumpir
          </button>
          <button onClick={clearQueue} style={{ padding: "8px", backgroundColor: "#6c757d", color: "white", border: "none", borderRadius: "4px" }}>
            Limpiar Cola
          </button>
          <button onClick={stopAll} style={{ padding: "8px", backgroundColor: "#343a40", color: "white", border: "none", borderRadius: "4px" }}>
            Parar Todo
          </button>
        </div>
      </div>

      {/* Información de ayuda */}
      <div style={{ marginTop: "20px", padding: "10px", backgroundColor: "#d1ecf1", borderRadius: "4px", fontSize: "14px" }}>
        <h5>💡 Cómo usar este panel:</h5>
        <ul>
          <li><strong>Pruebas por tipo:</strong> Verifica que las prioridades funcionan correctamente</li>
          <li><strong>Pruebas por canal:</strong> Comprueba que MHC y Web Audio funcionan independientemente</li>
          <li><strong>Solapamientos:</strong> Lanza múltiples speech para verificar que no se solapan</li>
          <li><strong>Cola:</strong> Añade varios mensajes para probar el sistema de cola</li>
          <li><strong>Controles:</strong> Interrumpe, limpia o para todo el speech</li>
        </ul>
      </div>
    </div>
  );
};
