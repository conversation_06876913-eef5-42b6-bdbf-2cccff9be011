// components/AudioDebugPanel.tsx
import { useState } from "react";
import { useSpeechOutput } from "../../contexts/SpeechOutputContext";
import { useGameSpeech } from "../../hooks/useSpeechCoordinator";

export const AudioDebugPanel: React.FC = () => {
  const {
    state: { audioState, isMusicPlaying, isSpeechPlaying, isReady },
    playBackgroundMusic,
    pauseMusic,
    resumeMusic,
    stopMusic,
    setMusicVolume,
    setSpeechVolume,
    toggleMute,
    speakGameMessage,
    configure
  } = useSpeechOutput();

  const gameSpeech = useGameSpeech(); // 🔧 NUEVO: Hook del coordinador

  const [testText, setTestText] = useState("Hola, soy Enygma y estoy probando mi voz");

  const handleTestSpeech = async () => {
    try {
      // 🔧 CAMBIO: Usar coordinador de speech
      await gameSpeech.speakResponse(testText);
    } catch (error) {
      console.error("Error en test speech:", error);
    }
  };

  const handleConfigureVoice = async () => {
    try {
      await configure("female");
    } catch (error) {
      console.error("Error configurando voz:", error);
    }
  };

  const handleTestMusic = async () => {
    try {
      await playBackgroundMusic();
    } catch (error) {
      console.error("Error iniciando música:", error);
    }
  };

  return (
    <div style={{
      position: 'fixed',
      top: '10px',
      right: '10px',
      width: '300px',
      background: 'rgba(0,0,0,0.9)',
      color: 'white',
      padding: '16px',
      borderRadius: '8px',
      fontSize: '12px',
      zIndex: 10000,
      fontFamily: 'monospace'
    }}>
      <h3 style={{ margin: '0 0 12px 0', color: '#88FFD5' }}>🎵 Audio Debug Panel</h3>

      {/* Estado */}
      <div style={{ marginBottom: '12px', fontSize: '11px' }}>
        <div>🎵 Música: {isMusicPlaying ? '▶️ Playing' : '⏸️ Stopped'}</div>
        <div>🗣️ Narración: {isSpeechPlaying ? '▶️ Speaking' : '🔇 Silent'}</div>
        <div>🔊 Muted: {audioState.isMuted ? '🔇 YES' : '🔊 NO'}</div>
        <div>✅ Voice Ready: {isReady ? '✅' : '❌'}</div>
        <div>🎶 Track: {audioState.music.currentTrack || 'None'}</div>
      </div>

      {/* Controles de voz */}
      <div style={{ marginBottom: '12px' }}>
        <h4 style={{ margin: '0 0 8px 0', color: '#88FFD5', fontSize: '11px' }}>🗣️ Speech</h4>
        <button
          onClick={handleConfigureVoice}
          style={buttonStyle}
        >
          Configure Voice
        </button>
        <input
          type="text"
          value={testText}
          onChange={(e) => setTestText(e.target.value)}
          style={{
            width: '100%',
            padding: '4px',
            margin: '4px 0',
            background: 'rgba(255,255,255,0.1)',
            border: '1px solid #88FFD5',
            borderRadius: '4px',
            color: 'white',
            fontSize: '11px'
          }}
          placeholder="Texto para probar..."
        />
        <button
          onClick={handleTestSpeech}
          disabled={!isReady}
          style={buttonStyle}
        >
          Test Speech
        </button>
      </div>

      {/* Controles de música */}
      <div style={{ marginBottom: '12px' }}>
        <h4 style={{ margin: '0 0 8px 0', color: '#88FFD5', fontSize: '11px' }}>🎵 Music</h4>
        <div style={{ display: 'flex', gap: '4px', flexWrap: 'wrap' }}>
          <button onClick={handleTestMusic} style={buttonStyle}>Play</button>
          <button onClick={pauseMusic} style={buttonStyle}>Pause</button>
          <button onClick={resumeMusic} style={buttonStyle}>Resume</button>
          <button onClick={stopMusic} style={buttonStyle}>Stop</button>
        </div>
      </div>

      {/* Controles de volumen */}
      <div style={{ marginBottom: '12px' }}>
        <h4 style={{ margin: '0 0 8px 0', color: '#88FFD5', fontSize: '11px' }}>🔊 Volume</h4>
        <div style={{ marginBottom: '6px' }}>
          <label style={{ fontSize: '10px' }}>Music: {Math.round(audioState.music.volume * 100)}%</label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={audioState.music.volume}
            onChange={(e) => setMusicVolume(parseFloat(e.target.value))}
            style={{ width: '100%', margin: '2px 0' }}
          />
        </div>
        <div style={{ marginBottom: '6px' }}>
          <label style={{ fontSize: '10px' }}>Speech: {Math.round(audioState.speech.volume * 100)}%</label>
          <input
            type="range"
            min="0"
            max="1"
            step="0.1"
            value={audioState.speech.volume}
            onChange={(e) => setSpeechVolume(parseFloat(e.target.value))}
            style={{ width: '100%', margin: '2px 0' }}
          />
        </div>
      </div>

      {/* Control general */}
      <button
        onClick={toggleMute}
        style={{
          ...buttonStyle,
          background: audioState.isMuted ? '#ff6464' : '#88FFD5',
          color: audioState.isMuted ? 'white' : 'black',
          width: '100%'
        }}
      >
        {audioState.isMuted ? '🔇 Unmute All' : '🔊 Mute All'}
      </button>
    </div>
  );
};

const buttonStyle = {
  padding: '4px 8px',
  margin: '2px',
  background: 'rgba(136, 255, 213, 0.2)',
  border: '1px solid #88FFD5',
  borderRadius: '4px',
  color: '#88FFD5',
  cursor: 'pointer',
  fontSize: '10px'
} as const;
