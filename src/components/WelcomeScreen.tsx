// components/WelcomeScreen.tsx
import { useState } from "react";
import { useSpeechOutput } from "../contexts/SpeechOutputContext";
import { useGameSpeech } from "../hooks/useSpeechCoordinator";

interface WelcomeScreenProps {
  onGameReady: () => void; // Callback cuando todo esté listo para empezar
}

export const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onGameReady }) => {
  const [isInitializing, setIsInitializing] = useState(false);

  const {
    configure,
    speakGameMessage,
    state: { isReady: speechReady }
  } = useSpeechOutput();

  const gameSpeech = useGameSpeech(); // 🔧 NUEVO: Hook del coordinador

  const handleStartGame = async () => {
    setIsInitializing(true);

    try {
      // 1. Configurar voz de Azure si no está lista
      if (!speechReady) {
        console.log("🔧 Configurando voz de Azure...");
        await configure("female");
      }

      // 2. Reproducir mensaje de bienvenida
      console.log("🗣️ Reproduciendo mensaje de bienvenida...");

      // 3. Aquí puedes agregar música de fondo si tienes
      // await startBackgroundMusic();

      // 4. Notificar que todo está listo
      onGameReady();

    } catch (error) {
      console.error("❌ Error inicializando el juego:", error);
      // Podrías mostrar un mensaje de error aquí
    } finally {
      setIsInitializing(false);
    }
  };

  return (
    <>
      {/* Overlay con efecto blur */}
      <div
        style={{
          position: "fixed",
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: "rgba(0, 20, 40, 0.95)",
          backdropFilter: "blur(8px)",
          zIndex: 9998,
        }}
      />

      {/* Pantalla de bienvenida */}
      <div
        style={{
          position: "fixed",
          top: "50%",
          left: "50%",
          transform: "translate(-50%, -50%)",
          width: "90%",
          maxWidth: "600px",
          padding: "60px 40px",
          textAlign: "center",
          zIndex: 9999,
          animation: "mysticalAppear 1s ease-out",
        }}
      >
        {/* Título místico */}
        <h1
          style={{
            fontSize: "clamp(28px, 5vw, 48px)",
            fontWeight: "600",
            color: "#88FFD5",
            marginBottom: "40px",
            lineHeight: "1.3",
            textShadow: "0 0 20px rgba(136, 255, 213, 0.5)",
            fontFamily: "inherit", // Usar la fuente OnAir
          }}
        >
          El velo del misterio se alza.
        </h1>

        {/* Subtítulo */}
        <p
          style={{
            fontSize: "clamp(18px, 3vw, 24px)",
            color: "#e0e0e0",
            marginBottom: "60px",
            lineHeight: "1.6",
            maxWidth: "500px",
            margin: "0 auto 60px auto",
            opacity: "0.9",
          }}
        >
          ¿Estás listo para enfrentarte a Enygma y desvelar el personaje oculto
          en el que está pensando?
        </p>

        {/* Imagen decorativa opcional */}
        <div
          style={{
            width: "120px",
            height: "120px",
            margin: "0 auto 40px auto",
            background: "radial-gradient(circle, rgba(136, 255, 213, 0.2) 0%, transparent 70%)",
            borderRadius: "50%",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            border: "2px solid rgba(136, 255, 213, 0.3)",
            animation: "pulse 2s infinite",
          }}
        >
          <div
            style={{
              fontSize: "48px",
              color: "#88FFD5",
              textShadow: "0 0 10px rgba(136, 255, 213, 0.8)",
            }}
          >
            🔮
          </div>
        </div>

        {/* Botón de empezar */}
        <button
          onClick={handleStartGame}
          disabled={isInitializing}
          style={{
            backgroundColor: isInitializing ? "#1e40af" : "#88FFD5",
            color: isInitializing ? "#ffffff" : "#001428",
            border: "none",
            borderRadius: "12px",
            padding: "16px 48px",
            fontSize: "20px",
            fontWeight: "600",
            cursor: isInitializing ? "not-allowed" : "pointer",
            transition: "all 0.3s ease",
            boxShadow: isInitializing
              ? "0 4px 20px rgba(30, 64, 175, 0.4)"
              : "0 4px 20px rgba(136, 255, 213, 0.4)",
            minWidth: "160px",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            gap: "12px",
            margin: "0 auto",
            transform: isInitializing ? "scale(0.98)" : "scale(1)",
          }}
          onMouseEnter={(e) => {
            if (!isInitializing) {
              e.currentTarget.style.transform = "scale(1.05)";
              e.currentTarget.style.boxShadow = "0 6px 25px rgba(136, 255, 213, 0.6)";
            }
          }}
          onMouseLeave={(e) => {
            if (!isInitializing) {
              e.currentTarget.style.transform = "scale(1)";
              e.currentTarget.style.boxShadow = "0 4px 20px rgba(136, 255, 213, 0.4)";
            }
          }}
        >
          {isInitializing ? (
            <>
              <div
                style={{
                  width: "20px",
                  height: "20px",
                  border: "2px solid #ffffff30",
                  borderTop: "2px solid #ffffff",
                  borderRadius: "50%",
                  animation: "spin 1s linear infinite",
                }}
              />
              Preparando...
            </>
          ) : (
            <>
              <span>✨</span>
              Empezar
            </>
          )}
        </button>

        {/* Texto sutil */}
        {isInitializing && (
          <p
            style={{
              marginTop: "20px",
              fontSize: "14px",
              color: "#94a3b8",
              opacity: "0.8",
              animation: "fadeIn 0.5s ease-in",
            }}
          >
            Configurando la experiencia mágica...
          </p>
        )}
      </div>

      {/* CSS Animations */}
      <style>{`
        @keyframes mysticalAppear {
          from {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.9) rotateY(10deg);
          }
          to {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1) rotateY(0deg);
          }
        }

        @keyframes pulse {
          0%, 100% {
            transform: scale(1);
            opacity: 0.8;
          }
          50% {
            transform: scale(1.05);
            opacity: 1;
          }
        }

        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 0.8; }
        }

        /* Responsive design */
        @media (max-width: 768px) {
          /* Ajustes para móvil si es necesario */
        }
      `}</style>
    </>
  );
};
