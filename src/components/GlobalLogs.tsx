import React, { useState, useEffect, useMemo } from "react";
import {
  logService,
  type LogEntry,
  type LogLevel,
  type LogFilter,
} from "../services/LogService";

interface GlobalLogsProps {
  maxHeight?: string;
  position?: "fixed" | "relative";
  defaultVisible?: boolean;
}

export const GlobalLogs: React.FC<GlobalLogsProps> = ({
  maxHeight = "300px",
  position = "fixed",
  defaultVisible = false,
}) => {
  const [logs, setLogs] = useState<LogEntry[]>([]);
  const [isVisible, setIsVisible] = useState(defaultVisible);
  const [filter, setFilter] = useState<LogFilter>({});
  const [autoScroll, setAutoScroll] = useState(true);

  // ========== EFFECTS ==========
  useEffect(() => {
    const removeListener = logService.addListener((newLogs) => {
      setLogs(newLogs);
    });

    return removeListener;
  }, []);

  // ========== COMPUTED VALUES ==========
  const filteredLogs = useMemo(() => {
    return logService.getLogs(filter);
  }, [logs, filter]);

  const stats = useMemo(() => {
    return logService.getStats();
  }, [logs]);

  // ========== EVENT HANDLERS ==========
  const handleClearLogs = () => {
    logService.clearLogs();
  };

  const handleExportLogs = () => {
    const data = logService.exportLogs();
    const blob = new Blob([data], { type: "application/json" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `enygma-logs-${new Date().toISOString().split("T")[0]}.json`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const handleLevelFilter = (level: LogLevel) => {
    setFilter((prev) => ({
      ...prev,
      levels: prev.levels?.includes(level)
        ? prev.levels.filter((l) => l !== level)
        : [...(prev.levels || []), level],
    }));
  };

  const handleServiceFilter = (service: string) => {
    setFilter((prev) => ({
      ...prev,
      services: prev.services?.includes(service)
        ? prev.services.filter((s) => s !== service)
        : [...(prev.services || []), service],
    }));
  };

  const getLevelColor = (level: LogLevel): string => {
    const colors = {
      debug: "#6b7280",
      info: "#3b82f6",
      warn: "#f59e0b",
      error: "#dc2626",
      success: "#10b981",
    };
    return colors[level];
  };

  const getLevelIcon = (level: LogLevel): string => {
    const icons = {
      debug: "🔍",
      info: "ℹ️",
      warn: "⚠️",
      error: "❌",
      success: "✅",
    };
    return icons[level];
  };

  const uniqueServices = useMemo(() => {
    const services = new Set(logs.map((log) => log.service));
    return Array.from(services).sort();
  }, [logs]);

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        style={{
          position: position,
          bottom: "20px",
          right: "20px",
          zIndex: 9999,
          backgroundColor: "#1f2937",
          color: "white",
          border: "none",
          borderRadius: "50%",
          width: "50px",
          height: "50px",
          cursor: "pointer",
          fontSize: "18px",
          boxShadow: "0 4px 12px rgba(0,0,0,0.3)",
        }}
        title="Mostrar Logs Globales"
      >
        📋
      </button>
    );
  }

  return (
    <div
      style={{
        position: position,
        bottom: "40px",
        right: "20px",
        width: "600px",
        maxHeight,
        backgroundColor: "#1f2937",
        color: "white",
        borderRadius: "12px",
        boxShadow: "0 10px 25px rgba(0,0,0,0.3)",
        zIndex: 9999,
        fontFamily: "system-ui, sans-serif",
        fontSize: "12px",
      }}
    >
      {/* Header */}
      <div
        style={{
          padding: "12px 16px",
          backgroundColor: "#111827",
          borderRadius: "12px 12px 0 0",
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
        }}
      >
        <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <span style={{ fontSize: "16px" }}>📋</span>
          <span style={{ fontWeight: "600" }}>Global Logs</span>
          <span
            style={{
              backgroundColor: "#374151",
              padding: "2px 6px",
              borderRadius: "4px",
              fontSize: "10px",
            }}
          >
            {filteredLogs.length}
          </span>
        </div>

        <div style={{ display: "flex", gap: "8px" }}>
          <button
            onClick={() => setAutoScroll(!autoScroll)}
            style={{
              backgroundColor: autoScroll ? "#059669" : "#6b7280",
              color: "white",
              border: "none",
              borderRadius: "4px",
              padding: "4px 8px",
              cursor: "pointer",
              fontSize: "10px",
            }}
            title="Auto Scroll"
          >
            📜
          </button>

          <button
            onClick={handleExportLogs}
            style={{
              backgroundColor: "#3b82f6",
              color: "white",
              border: "none",
              borderRadius: "4px",
              padding: "4px 8px",
              cursor: "pointer",
              fontSize: "10px",
            }}
            title="Exportar Logs"
          >
            💾
          </button>

          <button
            onClick={handleClearLogs}
            style={{
              backgroundColor: "#dc2626",
              color: "white",
              border: "none",
              borderRadius: "4px",
              padding: "4px 8px",
              cursor: "pointer",
              fontSize: "10px",
            }}
            title="Limpiar Logs"
          >
            🗑️
          </button>

          <button
            onClick={() => setIsVisible(false)}
            style={{
              backgroundColor: "#6b7280",
              color: "white",
              border: "none",
              borderRadius: "4px",
              padding: "4px 8px",
              cursor: "pointer",
              fontSize: "10px",
            }}
            title="Ocultar"
          >
            ❌
          </button>
        </div>
      </div>

      {/* Filters */}
      <div
        style={{
          padding: "8px 16px",
          backgroundColor: "#374151",
          borderBottom: "1px solid #4b5563",
        }}
      >
        {/* Level Filters */}
        <div style={{ marginBottom: "8px" }}>
          <span
            style={{ fontSize: "10px", color: "#d1d5db", marginRight: "8px" }}
          >
            Levels:
          </span>
          {(["debug", "info", "warn", "error", "success"] as LogLevel[]).map(
            (level) => (
              <button
                key={level}
                onClick={() => handleLevelFilter(level)}
                style={{
                  backgroundColor: filter.levels?.includes(level)
                    ? getLevelColor(level)
                    : "#6b7280",
                  color: "white",
                  border: "none",
                  borderRadius: "3px",
                  padding: "2px 6px",
                  margin: "0 2px",
                  cursor: "pointer",
                  fontSize: "9px",
                }}
              >
                {getLevelIcon(level)} {level}
              </button>
            ),
          )}
        </div>

        {/* Service Filters */}
        <div>
          <span
            style={{ fontSize: "10px", color: "#d1d5db", marginRight: "8px" }}
          >
            Services:
          </span>
          {uniqueServices.slice(0, 6).map((service) => (
            <button
              key={service}
              onClick={() => handleServiceFilter(service)}
              style={{
                backgroundColor: filter.services?.includes(service)
                  ? "#8b5cf6"
                  : "#6b7280",
                color: "white",
                border: "none",
                borderRadius: "3px",
                padding: "2px 6px",
                margin: "0 2px",
                cursor: "pointer",
                fontSize: "9px",
              }}
            >
              {service}
            </button>
          ))}
        </div>
      </div>

      {/* Logs Content */}
      <div
        style={{
          maxHeight: `calc(${maxHeight} - 120px)`,
          overflowY: "auto",
          padding: "8px",
        }}
      >
        {filteredLogs.length === 0 ? (
          <div
            style={{
              textAlign: "center",
              color: "#9ca3af",
              padding: "20px",
              fontSize: "11px",
            }}
          >
            No hay logs que mostrar
          </div>
        ) : (
          filteredLogs.map((log) => (
            <div
              key={log.id}
              style={{
                marginBottom: "4px",
                padding: "6px 8px",
                backgroundColor: "#111827",
                borderRadius: "4px",
                borderLeft: `3px solid ${getLevelColor(log.level)}`,
              }}
            >
              <div
                style={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "flex-start",
                  marginBottom: "2px",
                }}
              >
                <div
                  style={{ display: "flex", alignItems: "center", gap: "6px" }}
                >
                  <span>{getLevelIcon(log.level)}</span>
                  <span
                    style={{
                      color: "#9ca3af",
                      fontSize: "10px",
                    }}
                  >
                    {log.timestamp.toLocaleTimeString()}
                  </span>
                  <span
                    style={{
                      backgroundColor: "#374151",
                      color: "#d1d5db",
                      padding: "1px 4px",
                      borderRadius: "2px",
                      fontSize: "9px",
                    }}
                  >
                    {log.service}
                  </span>
                </div>
              </div>

              <div
                style={{
                  color: "#f3f4f6",
                  fontSize: "11px",
                  lineHeight: "1.3",
                  marginLeft: "20px",
                }}
              >
                {log.message}
              </div>

              {log.data && (
                <details style={{ marginTop: "4px", marginLeft: "20px" }}>
                  <summary
                    style={{
                      cursor: "pointer",
                      color: "#9ca3af",
                      fontSize: "10px",
                    }}
                  >
                    Data
                  </summary>
                  <pre
                    style={{
                      color: "#d1d5db",
                      fontSize: "9px",
                      backgroundColor: "#0f172a",
                      padding: "4px",
                      borderRadius: "2px",
                      marginTop: "2px",
                      overflow: "auto",
                      maxHeight: "100px",
                    }}
                  >
                    {typeof log.data === "string"
                      ? log.data
                      : JSON.stringify(log.data, null, 2)}
                  </pre>
                </details>
              )}

              {log.stack && (
                <details style={{ marginTop: "4px", marginLeft: "20px" }}>
                  <summary
                    style={{
                      cursor: "pointer",
                      color: "#f87171",
                      fontSize: "10px",
                    }}
                  >
                    Stack Trace
                  </summary>
                  <pre
                    style={{
                      color: "#fca5a5",
                      fontSize: "9px",
                      backgroundColor: "#0f172a",
                      padding: "4px",
                      borderRadius: "2px",
                      marginTop: "2px",
                      overflow: "auto",
                      maxHeight: "100px",
                    }}
                  >
                    {log.stack}
                  </pre>
                </details>
              )}
            </div>
          ))
        )}
      </div>

      {/* Stats Footer */}
      <div
        style={{
          padding: "8px 16px",
          backgroundColor: "#111827",
          borderRadius: "0 0 12px 12px",
          borderTop: "1px solid #4b5563",
          fontSize: "10px",
          color: "#9ca3af",
        }}
      >
        <div style={{ display: "flex", justifyContent: "space-between" }}>
          <span>Total: {(stats as any).totalLogs}</span>
          <span>Services: {uniqueServices.length}</span>
          <span>Errors: {(stats as any).levelStats?.error || 0}</span>
        </div>
      </div>
    </div>
  );
};
