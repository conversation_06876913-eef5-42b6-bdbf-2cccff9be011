// contexts/EventBusContext.tsx
import { createContext, useContext, useCallback, useState, type ReactNode } from "react";

type EventType =
  | 'game:start'
  | 'game:end'
  | 'speech:input'
  | 'speech:output'
  | 'ui:navigate';

interface GameEvent {
  type: EventType;
  payload?: any;
  timestamp: Date;
}

interface EventBusContextProps {
  emit: (type: EventType, payload?: any) => void;
  subscribe: (type: EventType, callback: (event: GameEvent) => void) => () => void;
  getLastEvent: (type: EventType) => GameEvent | null;
}

const EventBusContext = createContext<EventBusContextProps | undefined>(undefined);

export const useEventBus = () => {
  const context = useContext(EventBusContext);
  if (!context) {
    throw new Error("useEventBus must be used within EventBusProvider");
  }
  return context;
};

export const EventBusProvider = ({ children }: { children: ReactNode }) => {
  const [listeners, setListeners] = useState<Map<EventType, Set<(event: GameEvent) => void>>>(
    new Map()
  );
  const [eventHistory, setEventHistory] = useState<GameEvent[]>([]);

  const emit = useCallback((type: EventType, payload?: any) => {
    const event: GameEvent = {
      type,
      payload,
      timestamp: new Date()
    };

    setEventHistory(prev => [event, ...prev.slice(0, 99)]); // Keep last 100 events

    const typeListeners = listeners.get(type);
    if (typeListeners) {
      typeListeners.forEach(callback => {
        try {
          callback(event);
        } catch (error) {
          console.error(`Error in event listener for ${type}:`, error);
        }
      });
    }
  }, [listeners]);

  const subscribe = useCallback((type: EventType, callback: (event: GameEvent) => void) => {
    setListeners(prev => {
      const newListeners = new Map(prev);
      if (!newListeners.has(type)) {
        newListeners.set(type, new Set());
      }
      newListeners.get(type)!.add(callback);
      return newListeners;
    });

    // Return unsubscribe function
    return () => {
      setListeners(prev => {
        const newListeners = new Map(prev);
        const typeListeners = newListeners.get(type);
        if (typeListeners) {
          typeListeners.delete(callback);
          if (typeListeners.size === 0) {
            newListeners.delete(type);
          }
        }
        return newListeners;
      });
    };
  }, []);

  const getLastEvent = useCallback((type: EventType): GameEvent | null => {
    return eventHistory.find(event => event.type === type) || null;
  }, [eventHistory]);

  return (
    <EventBusContext.Provider value={{ emit, subscribe, getLastEvent }}>
      {children}
    </EventBusContext.Provider>
  );
};

// Ejemplo de uso en GameOrchestratorContext.tsx
/*
export const GameOrchestratorProvider = ({ children }: { children: ReactNode }) => {
  const { emit, subscribe } = useEventBus();
  const game = useEnygmaGame();

  useEffect(() => {
    // Suscribirse a eventos en lugar de importar directamente otros contextos
    const unsubscribeInput = subscribe('speech:input', (event) => {
      // Manejar input de voz
      handleUserInteraction(event.payload, 'speech');
    });

    const unsubscribeGameEnd = subscribe('game:end', (event) => {
      // Manejar fin de juego
      handleGameEnd(event.payload);
    });

    return () => {
      unsubscribeInput();
      unsubscribeGameEnd();
    };
  }, [subscribe]);

  const startGameFlow = useCallback(async (mode: GameMode) => {
    // Emitir evento en lugar de llamar directamente
    emit('game:start', { mode });

    await game.startNewGame(mode);

    // Notificar que el juego ha empezado
    emit('speech:output', {
      message: "¡Juego iniciado!",
      type: "system"
    });
  }, [emit, game]);

  // ... resto del contexto
};
*/