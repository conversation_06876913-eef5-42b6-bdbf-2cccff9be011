import {
  create<PERSON>ontext,
  useContext,
  useState,
  useEffect,
  useCallback,
  type ReactNode,
} from "react";
import { speechService } from "../services/SpeechService";
import { audioManager, type AudioState } from "../services/AudioManager";
import { speechCoordinator, type SpeechType } from "../services/SpeechCoordinator";
import { log } from "../services/LogService";

// ========== TYPES ==========
export type VoiceGender = "male" | "female";
export type MessageType = "system" | "question" | "answer" | "hint" | "victory" | "error" | "guess";
export type PlaybackState = "idle" | "playing" | "paused" | "loading" | "error";

// ========== MAPEO DE TIPOS ==========
const mapMessageTypeToSpeechType = (messageType: MessageType): SpeechType => {
  switch (messageType) {
    case "error":
      return "error";
    case "answer":
      return "game_response";
    case "victory":
      return "victory";
    case "question":
      return "question";
    case "hint":
      return "hint";
    case "system":
      return "info";
    case "guess":
      return "game_response";
    default:
      return "info";
  }
};

export interface SpeechOutputState {
  isReady: boolean;
  isConfiguring: boolean;
  playbackState: PlaybackState;
  currentVoice: string;
  availableVoices: string[];
  errorMessage: string | null;

  // 🆕 Estados de audio separados
  audioState: AudioState;
  isMusicPlaying: boolean;
  isSpeechPlaying: boolean;
}

export interface SpeechMessage {
  id: string;
  text: string;
  type: MessageType;
  timestamp: Date;
  duration?: number;
  voice?: string;
}

export interface SpeechOutputContextProps {
  // Current state
  state: SpeechOutputState;

  // Basic speech controls
  speak: (text: string) => Promise<void>;
  pause: () => void;
  resume: () => void;
  stop: () => void;

  // 🆕 Controles de música independientes
  playBackgroundMusic: () => Promise<void>;
  pauseMusic: () => void;
  resumeMusic: () => void;
  stopMusic: () => void;
  setMusicVolume: (volume: number) => void;

  // 🆕 Controles de narración independientes
  pauseSpeech: () => void;
  resumeSpeech: () => void;
  stopSpeech: () => void;
  setSpeechVolume: (volume: number) => void;

  // 🆕 Control master
  toggleMute: () => boolean;
  pauseAll: () => void;
  resumeAll: () => void;
  stopAll: () => void;

  // Configuration
  configure: (gender: VoiceGender) => Promise<boolean>;

  // Game-specific speech
  speakGameMessage: (message: string, type: MessageType) => Promise<void>;
  speakWithEmotion: (message: string, emotion: "excited" | "thoughtful" | "confident" | "disappointed") => Promise<void>;

  // Character voices
  setCharacterVoice: (character: string) => Promise<boolean>;
  getVoiceForCharacter: (character: string) => VoiceGender;

  // Queue management
  speakQueue: (messages: string[]) => Promise<void>;
  clearQueue: () => void;
  skipCurrent: () => void;

  // Utilities
  getAvailableVoices: () => string[];
  testVoice: (text?: string) => Promise<void>;
  reset: () => void;

  // Message history
  messageHistory: SpeechMessage[];
  getLastMessage: () => SpeechMessage | null;
  replayLastMessage: () => Promise<void>;
}

// ========== CHARACTER VOICE DATABASE ==========
const CHARACTER_VOICES = {
  // Male characters
  "batman": "male", "superman": "male", "iron man": "male", "thor": "male",
  "captain america": "male", "hulk": "male", "spiderman": "male", "spider-man": "male",
  "wolverine": "male", "deadpool": "male", "joker": "male", "harry potter": "male",
  "gandalf": "male", "yoda": "male", "darth vader": "male", "luke skywalker": "male",
  "indiana jones": "male", "james bond": "male", "sherlock holmes": "male",

  // Female characters
  "wonder woman": "female", "black widow": "female", "catwoman": "female",
  "hermione granger": "female", "leia organa": "female", "princess leia": "female",
  "katniss everdeen": "female", "elsa": "female", "anna": "female", "moana": "female",
  "mulan": "female", "pocahontas": "female", "ariel": "female", "belle": "female",

  // Default patterns
  "man": "male", "boy": "male", "guy": "male", "king": "male", "prince": "male",
  "woman": "female", "girl": "female", "queen": "female", "princess": "female"
} as const;

// ========== CONTEXT ==========
const SpeechOutputContext = createContext<SpeechOutputContextProps | undefined>(undefined);

export const useSpeechOutput = () => {
  const context = useContext(SpeechOutputContext);
  if (!context) {
    throw new Error("useSpeechOutput must be used within SpeechOutputProvider");
  }
  return context;
};

// ========== PROVIDER ==========
export const SpeechOutputProvider = ({ children }: { children: ReactNode }) => {
  // ========== LOCAL STATE ==========
  const [state, setState] = useState<SpeechOutputState>({
    isReady: false,
    isConfiguring: false,
    playbackState: "idle",
    currentVoice: "",
    availableVoices: [],
    errorMessage: null,

    // 🆕 Estados de audio
    audioState: audioManager.getState(),
    isMusicPlaying: false,
    isSpeechPlaying: false,
  });

  const [messageHistory, setMessageHistory] = useState<SpeechMessage[]>([]);
  const [messageQueue, setMessageQueue] = useState<string[]>([]);
  const [isProcessingQueue, setIsProcessingQueue] = useState<boolean>(false);

  // ========== EFFECTS ==========
  useEffect(() => {
    log.info("speechOutput", "🔊 Inicializando SpeechOutputProvider con AudioManager");

    // Sincronizar estado inicial con el servicio
    updateStateFromService();

    // Suscribirse a cambios del AudioManager
    const removeAudioListener = audioManager.addListener((audioState) => {
      setState(prev => ({
        ...prev,
        audioState,
        isMusicPlaying: audioState.music.isPlaying,
        isSpeechPlaying: audioState.speech.isPlaying,
      }));
    });

    return () => {
      log.info("speechOutput", "🧹 Limpiando SpeechOutputProvider");
      removeAudioListener();
      stopAll();
      clearQueue();
    };
  }, []);

  // ========== HELPER FUNCTIONS ==========
  const updateStateFromService = useCallback(() => {
    setState(prev => ({
      ...prev,
      currentVoice: speechService.getCurrentVoiceId(),
      availableVoices: speechService.getAvailableVoicesList(),
      isReady: Boolean(speechService.getCurrentVoiceId()),
    }));
  }, []);

  const createMessage = useCallback((text: string, type: MessageType): SpeechMessage => {
    return {
      id: `msg-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      text,
      type,
      timestamp: new Date(),
      voice: state.currentVoice,
    };
  }, [state.currentVoice]);

  const addToHistory = useCallback((message: SpeechMessage) => {
    setMessageHistory(prev => [message, ...prev.slice(0, 49)]); // Keep last 50 messages
  }, []);

  // ========== 🆕 CONTROLES DE MÚSICA ==========
  const playBackgroundMusic = useCallback(async (): Promise<void> => {
    try {
      await audioManager.playMusic("/assets/sounds/sound.mp3");
      log.success("speechOutput", "🎵 Música de fondo iniciada");
    } catch (error) {
      log.error("speechOutput", "Error iniciando música de fondo", error);
      throw error;
    }
  }, []);

  const pauseMusic = useCallback(() => {
    audioManager.pauseMusic();
  }, []);

  const resumeMusic = useCallback(() => {
    audioManager.resumeMusic();
  }, []);

  const stopMusic = useCallback(() => {
    audioManager.stopMusic();
  }, []);

  const setMusicVolume = useCallback((volume: number) => {
    audioManager.setMusicVolume(volume);
  }, []);

  // ========== 🆕 CONTROLES DE NARRACIÓN ==========
  const pauseSpeech = useCallback(() => {
    audioManager.pauseSpeech();
  }, []);

  const resumeSpeech = useCallback(() => {
    audioManager.resumeSpeech();
  }, []);

  const stopSpeech = useCallback(() => {
    audioManager.stopSpeech();
  }, []);

  const setSpeechVolume = useCallback((volume: number) => {
    audioManager.setSpeechVolume(volume);
  }, []);

  // ========== 🆕 CONTROL MASTER ==========
  const toggleMute = useCallback((): boolean => {
    return audioManager.toggleMute();
  }, []);

  const pauseAll = useCallback(() => {
    audioManager.pauseAll();
  }, []);

  const resumeAll = useCallback(() => {
    audioManager.resumeAll();
  }, []);

  const stopAll = useCallback(() => {
    audioManager.stopAll();
  }, []);

  // ========== BASIC SPEECH CONTROLS (REFACTORIZADO CON COORDINADOR) ==========
  const speak = useCallback(async (text: string): Promise<void> => {
    if (!text.trim()) {
      log.warn("speechOutput", "⚠️ Texto vacío para speak()");
      return;
    }

    setState(prev => ({ ...prev, playbackState: "loading", errorMessage: null }));

    try {
      const startTime = Date.now();
      const message = createMessage(text, "system");

      // 🔧 CAMBIO: Usar SpeechCoordinator en lugar de llamadas directas
      await speechCoordinator.speak(text, "info", {
        onStart: () => {
          setState(prev => ({ ...prev, playbackState: "playing" }));
        },
        onComplete: () => {
          const duration = Date.now() - startTime;
          message.duration = duration;
          addToHistory(message);
          setState(prev => ({ ...prev, playbackState: "idle" }));
          log.success("speechOutput", "✅ Speech completado", { duration });
        },
        onError: (error) => {
          setState(prev => ({
            ...prev,
            playbackState: "error",
            errorMessage: error.message
          }));
          log.error("speechOutput", "❌ Error en speech", error);
        }
      });

    } catch (error) {
      setState(prev => ({
        ...prev,
        playbackState: "error",
        errorMessage: error instanceof Error ? error.message : "Error desconocido"
      }));
      log.error("speechOutput", "❌ Error en speak()", error);
      throw error;
    }
  }, [createMessage, addToHistory]);

  const pause = useCallback(() => {
    pauseSpeech();
    setState(prev => ({ ...prev, playbackState: "paused" }));
  }, [pauseSpeech]);

  const resume = useCallback(() => {
    resumeSpeech();
    setState(prev => ({ ...prev, playbackState: "playing" }));
  }, [resumeSpeech]);

  const stop = useCallback(() => {
    stopSpeech();
    setState(prev => ({ ...prev, playbackState: "idle" }));
  }, [stopSpeech]);

  // ========== CONFIGURATION ==========
  const configure = useCallback(async (gender: VoiceGender): Promise<boolean> => {
    log.info("speechOutput", `🔧 Configurando voz: ${gender}`);

    setState(prev => ({ ...prev, isConfiguring: true, errorMessage: null }));

    try {
      const success = await speechService.configVoice(gender);

      if (success) {
        updateStateFromService();
        log.success("speechOutput", `✅ Voz configurada: ${gender}`);
      } else {
        throw new Error(`No se pudo configurar voz ${gender}`);
      }

      return success;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Error configurando voz";
      log.error("speechOutput", "❌ Error configurando voz", error);

      setState(prev => ({
        ...prev,
        errorMessage
      }));

      return false;

    } finally {
      setState(prev => ({ ...prev, isConfiguring: false }));
    }
  }, [updateStateFromService]);

  // ========== GAME-SPECIFIC SPEECH (REFACTORIZADO CON COORDINADOR) ==========
  const speakGameMessage = useCallback(async (message: string, type: MessageType): Promise<void> => {
    let enhancedMessage = message;

    // Añadir contexto emocional según el tipo
    switch (type) {
      case "question":
        enhancedMessage = message;
        break;
      case "answer":
        enhancedMessage = message;
        break;
      case "hint":
        enhancedMessage = `Te doy una pista: ${message}`;
        break;
      case "victory":
        enhancedMessage = `¡Excelente! ${message}`;
        break;
      case "error":
        enhancedMessage = `Lo siento, ${message}`;
        break;
      case "system":
        enhancedMessage = message;
        break;
    }

    log.info("speechOutput", `🎮 Speaking game message [${type}]: ${enhancedMessage.substring(0, 50)}...`);

    const gameMessage = createMessage(enhancedMessage, type);
    addToHistory(gameMessage);

    // 🔧 CAMBIO: Mapear MessageType a SpeechType y usar coordinador
    const speechType: SpeechType = mapMessageTypeToSpeechType(type);
    await speechCoordinator.speak(enhancedMessage, speechType);
  }, [createMessage, addToHistory]);

  const speakWithEmotion = useCallback(async (
    message: string,
    emotion: "excited" | "thoughtful" | "confident" | "disappointed"
  ): Promise<void> => {
    let emotionalMessage = message;

    switch (emotion) {
      case "excited":
        emotionalMessage = `¡${message}!`;
        break;
      case "thoughtful":
        emotionalMessage = `Hmm... ${message}`;
        break;
      case "confident":
        emotionalMessage = `Estoy segura: ${message}`;
        break;
      case "disappointed":
        emotionalMessage = `Oh... ${message}`;
        break;
    }

    log.info("speechOutput", `😊 Speaking with emotion [${emotion}]: ${message.substring(0, 50)}...`);

    // 🔧 CAMBIO: Usar coordinador con tipo apropiado según emoción
    const speechType: SpeechType = emotion === "excited" ? "victory" : "info";
    await speechCoordinator.speak(emotionalMessage, speechType);
  }, []);

  // ========== CHARACTER VOICES ==========
  const getVoiceForCharacter = useCallback((character: string): VoiceGender => {
    const normalizedChar = character.toLowerCase();

    // Buscar coincidencia exacta
    for (const [charName, gender] of Object.entries(CHARACTER_VOICES)) {
      if (normalizedChar.includes(charName)) {
        return gender as VoiceGender;
      }
    }

    // Fallback: usar patrones de género
    const malePatterns = ["mr", "señor", "king", "prince", "man", "boy"];
    const femalePatterns = ["ms", "mrs", "señora", "queen", "princess", "woman", "girl"];

    if (malePatterns.some(pattern => normalizedChar.includes(pattern))) {
      return "male";
    }

    if (femalePatterns.some(pattern => normalizedChar.includes(pattern))) {
      return "female";
    }

    // Default: female (Aura's voice)
    return "female";
  }, []);

  const setCharacterVoice = useCallback(async (character: string): Promise<boolean> => {
    const gender = getVoiceForCharacter(character);
    log.info("speechOutput", `🎭 Configurando voz para personaje: ${character} → ${gender}`);

    return await configure(gender);
  }, [getVoiceForCharacter, configure]);

  // ========== QUEUE MANAGEMENT ==========
  const speakQueue = useCallback(async (messages: string[]): Promise<void> => {
    if (isProcessingQueue) {
      log.warn("speechOutput", "⚠️ Ya se está procesando una cola");
      return;
    }

    log.info("speechOutput", `📋 Procesando cola de ${messages.length} mensajes`);
    setMessageQueue(messages);
    setIsProcessingQueue(true);

    try {
      // 🔧 CAMBIO: Usar coordinador para procesar cola
      for (const message of messages) {
        if (messageQueue.length === 0) break; // Cola cancelada

        await speechCoordinator.speak(message, "info");
        setMessageQueue(prev => prev.slice(1)); // Remover mensaje procesado

        // Pequeña pausa entre mensajes (el coordinador ya maneja timing)
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    } catch (error) {
      log.error("speechOutput", "❌ Error procesando cola", error);
    } finally {
      setIsProcessingQueue(false);
      setMessageQueue([]);
    }
  }, [isProcessingQueue, messageQueue]);

  const clearQueue = useCallback(() => {
    log.info("speechOutput", "🗑️ Limpiando cola de mensajes");
    setMessageQueue([]);
    setIsProcessingQueue(false);
    // 🔧 CAMBIO: También limpiar cola del coordinador
    speechCoordinator.clearQueue();
  }, []);

  const skipCurrent = useCallback(() => {
    log.info("speechOutput", "⏭️ Saltando mensaje actual");
    // 🔧 CAMBIO: Usar coordinador para interrumpir
    speechCoordinator.interrupt("critical");
    if (messageQueue.length > 1) {
      setMessageQueue(prev => prev.slice(1));
    } else {
      clearQueue();
    }
    setState(prev => ({ ...prev, playbackState: "idle" }));
  }, [messageQueue, clearQueue]);

  // ========== UTILITIES ==========
  const getAvailableVoices = useCallback((): string[] => {
    return speechService.getAvailableVoicesList();
  }, []);

  const testVoice = useCallback(async (text: string = "Hola, esta es una prueba de voz"): Promise<void> => {
    log.info("speechOutput", "🧪 Probando voz actual");
    await speak(text);
  }, [speak]);

  const reset = useCallback(() => {
    log.info("speechOutput", "🔄 Reseteando SpeechOutput");

    stopAll();
    clearQueue();

    setState({
      isReady: false,
      isConfiguring: false,
      playbackState: "idle",
      currentVoice: "",
      availableVoices: [],
      errorMessage: null,
      audioState: audioManager.getState(),
      isMusicPlaying: false,
      isSpeechPlaying: false,
    });

    setMessageHistory([]);
  }, [stopAll, clearQueue]);

  // ========== MESSAGE HISTORY ==========
  const getLastMessage = useCallback((): SpeechMessage | null => {
    return messageHistory[0] || null;
  }, [messageHistory]);

  const replayLastMessage = useCallback(async (): Promise<void> => {
    const lastMessage = getLastMessage();
    if (lastMessage) {
      log.info("speechOutput", "🔁 Repitiendo último mensaje");
      await speak(lastMessage.text);
    } else {
      log.warn("speechOutput", "⚠️ No hay mensajes para repetir");
    }
  }, [getLastMessage, speak]);

  // ========== CONTEXT VALUE ==========
  const contextValue: SpeechOutputContextProps = {
    // Current state
    state,

    // Basic speech controls
    speak,
    pause,
    resume,
    stop,

    // 🆕 Controles de música independientes
    playBackgroundMusic,
    pauseMusic,
    resumeMusic,
    stopMusic,
    setMusicVolume,

    // 🆕 Controles de narración independientes
    pauseSpeech,
    resumeSpeech,
    stopSpeech,
    setSpeechVolume,

    // 🆕 Control master
    toggleMute,
    pauseAll,
    resumeAll,
    stopAll,

    // Configuration
    configure,

    // Game-specific speech
    speakGameMessage,
    speakWithEmotion,

    // Character voices
    setCharacterVoice,
    getVoiceForCharacter,

    // Queue management
    speakQueue,
    clearQueue,
    skipCurrent,

    // Utilities
    getAvailableVoices,
    testVoice,
    reset,

    // Message history
    messageHistory,
    getLastMessage,
    replayLastMessage,
  };

  return (
    <SpeechOutputContext.Provider value={contextValue}>
      {children}
    </SpeechOutputContext.Provider>
  );
};
