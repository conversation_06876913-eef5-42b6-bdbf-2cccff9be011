import { createRoot } from "react-dom/client";
import { StrictMode } from "react";
import { AppProvider } from "./contexts/AppContext.tsx";
import { EventBusProvider } from "./contexts/EventBusContext.tsx";
import { MHCProvider } from "./contexts/MHCContext.tsx";
import { SpeechInputProvider } from "./contexts/SpeechInputContext.tsx";
import { SpeechOutputProvider } from "./contexts/SpeechOutputContext.tsx";
import { EnygmaGameProvider } from "./contexts/EnygmaGameContext.tsx";
import { MovistarPlusProvider } from "./contexts/MovistarPlusContext.tsx";
import { GameOrchestratorProvider } from "./contexts/GameOrchestratorContext.tsx";
import App from "./App.tsx";
import "./index.scss";

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <AppProvider>
      <EventBusProvider>
        <MHCProvider>
          <SpeechInputProvider>
            <SpeechOutputProvider>
              <MovistarPlusProvider>
                <EnygmaGameProvider>
                  <GameOrchestratorProvider>
                    <App />
                  </GameOrchestratorProvider>
                </EnygmaGameProvider>
              </MovistarPlusProvider>
            </SpeechOutputProvider>
          </SpeechInputProvider>
        </MHCProvider>
      </EventBusProvider>
    </AppProvider>
  </StrictMode>,
);
