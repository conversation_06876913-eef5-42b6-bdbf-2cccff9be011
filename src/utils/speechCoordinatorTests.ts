/**
 * ========================================================================
 * TESTS: Speech Coordinator Validation
 * ========================================================================
 * 
 * Scripts de prueba para validar que el coordinador de speech funciona
 * correctamente y no hay solapamientos
 * ========================================================================
 */

import { speechCoordinator } from "../services/SpeechCoordinator";

// ========== TIPOS DE PRUEBA ==========
interface TestResult {
  name: string;
  passed: boolean;
  message: string;
  duration?: number;
}

interface TestSuite {
  name: string;
  tests: TestResult[];
  passed: boolean;
  totalDuration: number;
}

// ========== UTILIDADES DE PRUEBA ==========
const wait = (ms: number): Promise<void> => new Promise(resolve => setTimeout(resolve, ms));

const measureTime = async <T>(fn: () => Promise<T>): Promise<{ result: T; duration: number }> => {
  const start = Date.now();
  const result = await fn();
  const duration = Date.now() - start;
  return { result, duration };
};

// ========== PRUEBAS INDIVIDUALES ==========

/**
 * Prueba básica de speech
 */
export const testBasicSpeech = async (): Promise<TestResult> => {
  try {
    const { duration } = await measureTime(async () => {
      await speechCoordinator.speak("Prueba básica de speech", "info");
    });

    return {
      name: "Basic Speech",
      passed: true,
      message: "Speech básico ejecutado correctamente",
      duration
    };
  } catch (error) {
    return {
      name: "Basic Speech",
      passed: false,
      message: `Error: ${error instanceof Error ? error.message : "Unknown error"}`
    };
  }
};

/**
 * Prueba de prioridades
 */
export const testPriorities = async (): Promise<TestResult> => {
  try {
    const startTime = Date.now();
    
    // Lanzar speech de baja prioridad
    speechCoordinator.speak("Mensaje de baja prioridad", "info");
    
    // Esperar un poco
    await wait(100);
    
    // Lanzar speech de alta prioridad (debería interrumpir)
    speechCoordinator.speak("Mensaje crítico", "error");
    
    // Verificar que el speech crítico se ejecuta
    await wait(2000);
    
    const duration = Date.now() - startTime;
    
    return {
      name: "Priority System",
      passed: true,
      message: "Sistema de prioridades funcionando correctamente",
      duration
    };
  } catch (error) {
    return {
      name: "Priority System",
      passed: false,
      message: `Error en prioridades: ${error instanceof Error ? error.message : "Unknown error"}`
    };
  }
};

/**
 * Prueba de cola
 */
export const testQueue = async (): Promise<TestResult> => {
  try {
    const startTime = Date.now();
    
    // Limpiar cola primero
    speechCoordinator.clearQueue();
    
    // Añadir múltiples mensajes de la misma prioridad
    const messages = [
      "Primer mensaje en cola",
      "Segundo mensaje en cola", 
      "Tercer mensaje en cola"
    ];
    
    for (const message of messages) {
      speechCoordinator.speak(message, "info");
    }
    
    // Verificar que la cola tiene elementos
    const queueLength = speechCoordinator.getQueueLength();
    
    if (queueLength === 0) {
      return {
        name: "Queue System",
        passed: false,
        message: "La cola no se llenó correctamente"
      };
    }
    
    // Esperar a que se procese
    await wait(5000);
    
    const duration = Date.now() - startTime;
    
    return {
      name: "Queue System",
      passed: true,
      message: `Cola procesada correctamente (${queueLength} mensajes)`,
      duration
    };
  } catch (error) {
    return {
      name: "Queue System",
      passed: false,
      message: `Error en cola: ${error instanceof Error ? error.message : "Unknown error"}`
    };
  }
};

/**
 * Prueba de interrupción
 */
export const testInterruption = async (): Promise<TestResult> => {
  try {
    const startTime = Date.now();
    
    // Iniciar speech largo
    speechCoordinator.speak("Este es un mensaje muy largo que debería ser interrumpido antes de terminar completamente", "info");
    
    // Esperar un poco
    await wait(500);
    
    // Interrumpir
    speechCoordinator.interrupt("critical");
    
    // Verificar que no está hablando
    await wait(100);
    const isSpeaking = speechCoordinator.isSpeaking();
    
    const duration = Date.now() - startTime;
    
    return {
      name: "Interruption System",
      passed: !isSpeaking,
      message: isSpeaking ? "Speech no fue interrumpido" : "Interrupción funcionó correctamente",
      duration
    };
  } catch (error) {
    return {
      name: "Interruption System",
      passed: false,
      message: `Error en interrupción: ${error instanceof Error ? error.message : "Unknown error"}`
    };
  }
};

/**
 * Prueba de métodos de conveniencia
 */
export const testConvenienceMethods = async (): Promise<TestResult> => {
  try {
    const startTime = Date.now();
    
    // Probar diferentes métodos de conveniencia
    await speechCoordinator.speakError("Error de prueba");
    await wait(100);
    
    await speechCoordinator.speakValidation("Validación de prueba");
    await wait(100);
    
    await speechCoordinator.speakGameResponse("Respuesta de prueba");
    await wait(100);
    
    await speechCoordinator.speakGameQuestion("Pregunta de prueba");
    await wait(100);
    
    const duration = Date.now() - startTime;
    
    return {
      name: "Convenience Methods",
      passed: true,
      message: "Métodos de conveniencia funcionando correctamente",
      duration
    };
  } catch (error) {
    return {
      name: "Convenience Methods",
      passed: false,
      message: `Error en métodos de conveniencia: ${error instanceof Error ? error.message : "Unknown error"}`
    };
  }
};

/**
 * Prueba de prevención de solapamientos
 */
export const testOverlapPrevention = async (): Promise<TestResult> => {
  try {
    const startTime = Date.now();
    
    // Lanzar múltiples speech rápidamente
    speechCoordinator.speak("Primer speech", "info");
    speechCoordinator.speak("Segundo speech", "info");
    speechCoordinator.speak("Tercer speech", "info");
    speechCoordinator.speak("Cuarto speech", "info");
    
    // Verificar que solo uno está reproduciéndose
    await wait(100);
    const isSpeaking = speechCoordinator.isSpeaking();
    const queueLength = speechCoordinator.getQueueLength();
    
    // Debería estar hablando uno y tener otros en cola
    const isWorking = isSpeaking && queueLength > 0;
    
    const duration = Date.now() - startTime;
    
    return {
      name: "Overlap Prevention",
      passed: isWorking,
      message: isWorking 
        ? `Prevención de solapamientos funcionando (hablando: ${isSpeaking}, en cola: ${queueLength})`
        : "No se previenen los solapamientos correctamente",
      duration
    };
  } catch (error) {
    return {
      name: "Overlap Prevention",
      passed: false,
      message: `Error en prevención de solapamientos: ${error instanceof Error ? error.message : "Unknown error"}`
    };
  }
};

// ========== SUITE DE PRUEBAS COMPLETA ==========

/**
 * Ejecutar todas las pruebas del coordinador de speech
 */
export const runSpeechCoordinatorTests = async (): Promise<TestSuite> => {
  console.log("🧪 Iniciando pruebas del Speech Coordinator...");
  
  const startTime = Date.now();
  const tests: TestResult[] = [];
  
  // Ejecutar pruebas secuencialmente
  tests.push(await testBasicSpeech());
  await wait(1000); // Pausa entre pruebas
  
  tests.push(await testPriorities());
  await wait(1000);
  
  tests.push(await testQueue());
  await wait(1000);
  
  tests.push(await testInterruption());
  await wait(1000);
  
  tests.push(await testConvenienceMethods());
  await wait(1000);
  
  tests.push(await testOverlapPrevention());
  
  const totalDuration = Date.now() - startTime;
  const passed = tests.every(test => test.passed);
  
  const suite: TestSuite = {
    name: "Speech Coordinator Tests",
    tests,
    passed,
    totalDuration
  };
  
  // Log resultados
  console.log(`\n📊 Resultados de las pruebas:`);
  console.log(`Suite: ${suite.name}`);
  console.log(`Estado: ${suite.passed ? "✅ PASÓ" : "❌ FALLÓ"}`);
  console.log(`Duración total: ${suite.totalDuration}ms`);
  console.log(`\nPruebas individuales:`);
  
  suite.tests.forEach(test => {
    const status = test.passed ? "✅" : "❌";
    const duration = test.duration ? ` (${test.duration}ms)` : "";
    console.log(`  ${status} ${test.name}${duration}: ${test.message}`);
  });
  
  return suite;
};

/**
 * Prueba rápida para verificar que no hay solapamientos
 */
export const quickOverlapTest = async (): Promise<boolean> => {
  console.log("⚡ Prueba rápida de solapamientos...");
  
  // Limpiar estado
  speechCoordinator.stopAll();
  await wait(100);
  
  // Lanzar múltiples speech
  speechCoordinator.speak("Test 1", "info");
  speechCoordinator.speak("Test 2", "info");
  speechCoordinator.speak("Test 3", "info");
  
  await wait(200);
  
  // Verificar estado
  const isSpeaking = speechCoordinator.isSpeaking();
  const queueLength = speechCoordinator.getQueueLength();
  
  const result = isSpeaking && queueLength >= 0;
  
  console.log(`Resultado: ${result ? "✅ Sin solapamientos" : "❌ Hay solapamientos"}`);
  console.log(`Estado: hablando=${isSpeaking}, cola=${queueLength}`);
  
  return result;
};
