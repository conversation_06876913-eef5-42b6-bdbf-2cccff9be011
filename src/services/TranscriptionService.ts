import { v4 as uuid } from "uuid";

// ========== TYPES ==========
export interface TranscriptionResponse {
  success: boolean;
  transcription?: string;
  error?: string;
  normalized?: string;
}

export interface TranscriptionEvent {
  type: "transcription" | "command" | "error";
  data: string;
  timestamp: Date;
  normalized?: string;
}

// ========== MHC INTERFACE ==========
export interface IMHC {
  setSucwTimeout(timeMS: number): void;
  closeWebView(): void;
  hideAura(): void;
  speakAura(text: string): void;
  onPageLoaded(): void;
  sendAura(text: string): void;
  getId(): string;
}

// ========== UTILITIES ==========
import { textNormalizer } from './TextNormalizationService';

// Función de compatibilidad - usar textNormalizer directamente en código nuevo
export const normalize = (str: string): Promise<string> => {
  return textNormalizer.normalizeAsync(str, {
    removeAccents: true,
    toLowerCase: true,
    trimWhitespace: true,
    preserveSpaces: true
  });
};

// ========== MHC IMPLEMENTATION ==========
export class MHC implements IMHC {
  private static instance: MHC;
  private mhcWarningShown: boolean = false;

  private constructor() {}

  public static getInstance(): MHC {
    if (!MHC.instance) {
      MHC.instance = new MHC();
    }
    return MHC.instance;
  }

  private isMHAvailable(): boolean {
    // @ts-ignore
    return typeof MH !== "undefined";
  }

  setSucwTimeout(timeMS: number): void {
    if (this.isMHAvailable()) {
      try {
        // @ts-ignore
        MH.sucwTimeout(timeMS);
        // console.log(`✅ MHC: Timeout establecido a ${timeMS}ms`);
      } catch (error) {
        // console.error("❌ MHC: Error setting timeout:", error);
      }
    } else {
      // console.warn("⚠️ MHC: MH no disponible para setSucwTimeout");
    }
  }

  closeWebView(): void {
    if (this.isMHAvailable()) {
      try {
        // @ts-ignore
        MH.closeWebview();
        // console.log("✅ MHC: WebView cerrado");
      } catch (error) {
        // console.error("❌ MHC: Error closing webview:", error);
      }
    } else {
      // console.warn("⚠️ MHC: MH no disponible para closeWebView");
    }
  }

  hideAura(): void {
    if (this.isMHAvailable()) {
      try {
        // @ts-ignore
        MH.hideAura();
        // console.log("✅ MHC: Aura ocultado");
      } catch (error) {
        // console.error("❌ MHC: Error hiding aura:", error);
      }
    } else {
      // console.warn("⚠️ MHC: MH no disponible para hideAura");
    }
  }

  speakAura(text: string): void {
    if (this.isMHAvailable()) {
      try {
        const payload = JSON.stringify({ text });
        // @ts-ignore
        MH.speak(payload);
        // console.log(`✅ MHC: Aura hablando: "${text.substring(0, 50)}..."`);
      } catch (error) {
        // console.error("❌ MHC: Error speaking:", error);
      }
    } else {
      // console.warn("⚠️ MHC: MH no disponible para speakAura");
    }
  }

  onPageLoaded(): void {
    if (this.isMHAvailable()) {
      try {
        // @ts-ignore
        MH.onPageLoaded();
        // console.log("✅ MHC: Página cargada notificada");
      } catch (error) {
        // console.error("❌ MHC: Error on page loaded:", error);
      }
    } else {
      // Solo mostrar warning una vez para evitar spam
      if (!this.mhcWarningShown) {
        // console.warn("⚠️ MHC: MH no disponible para funciones MHC");
      }
    }
  }

  sendAura(text: string): void {
    if (this.isMHAvailable()) {
      try {
        const payload = JSON.stringify({ text });
        // @ts-ignore
        MH.sendAura(payload);
        // console.log(`✅ MHC: Enviado a Aura: "${text.substring(0, 50)}..."`);
      } catch (error) {
        // console.error("❌ MHC: Error sending to aura:", error);
      }
    } else {
      // console.warn("⚠️ MHC: MH no disponible para sendAura");
    }
  }

  getId(): string {
    if (this.isMHAvailable()) {
      try {
        // @ts-ignore
        const id = MH.getId();
        // console.log(`✅ MHC: ID obtenido: ${id}`);
        return id;
      } catch (error) {
        // console.error("❌ MHC: Error getting ID:", error);
        return uuid();
      }
    } else {
      // Solo mostrar warning una vez por sesión para evitar spam
      if (!this.mhcWarningShown) {
        // console.warn("⚠️ MHC: MH no disponible, usando UUIDs como fallback");
        this.mhcWarningShown = true;
      }
      const fallbackId = uuid();
      return fallbackId;
    }
  }

  // ========== UTILITY METHODS ==========
  public isAvailable(): boolean {
    return this.isMHAvailable();
  }

  public getStatus(): object {
    return {
      available: this.isMHAvailable(),
      id: this.getId(),
      timestamp: new Date().toISOString(),
    };
  }
}

// ========== TRANSCRIPTION SERVICE ==========
class TranscriptionService {
  private static instance: TranscriptionService;
  private mhc: IMHC;
  private currentTranscription: string | null = null;
  private eventListeners: Set<(event: TranscriptionEvent) => void> = new Set();
  private isListening: boolean = false;

  // Comandos especiales que cierran la aplicación
  private readonly CLOSE_COMMANDS = ["salir", "cerrar", "exit", "close"];

  private constructor() {
    this.mhc = MHC.getInstance();
    this.setupGlobalTesting();
  }

  public static getInstance(): TranscriptionService {
    if (!TranscriptionService.instance) {
      TranscriptionService.instance = new TranscriptionService();
    }
    return TranscriptionService.instance;
  }

  // ========== GETTERS ==========
  public getCurrentTranscription(): string | null {
    return this.currentTranscription;
  }

  public isCurrentlyListening(): boolean {
    return this.isListening;
  }

  public getMHC(): IMHC {
    return this.mhc;
  }

  // ========== PRIVATE METHODS ==========
  private setupGlobalTesting(): void {
    try {
      // Exponer función de testing globalmente
      // @ts-ignore
      window.testTranscription = (text: string) => {
        this.simulateTranscription(text);
      };

      // @ts-ignore
      window.transcriptionService = this;

      // console.log(
      //   "🧪 TranscriptionService: Funciones de testing expuestas globalmente",
      // );
    } catch (error) {
      // console.warn(
      //   "⚠️ TranscriptionService: No se pudieron exponer funciones de testing:",
      //   error,
      // );
    }
  }

  private async processTranscription(
    rawText: string,
  ): Promise<TranscriptionResponse> {
    try {
      if (!rawText || rawText.trim() === "") {
        return {
          success: false,
          error: "Transcripción vacía",
        };
      }

      const normalizedText = await normalize(rawText);
      // console.log(
      //   `🎤 TranscriptionService: Procesando "${rawText}" → "${normalizedText}"`,
      // );

      // Verificar comandos de cierre
      if (this.CLOSE_COMMANDS.includes(normalizedText)) {
        // console.log("🚪 TranscriptionService: Comando de cierre detectado");
        this.handleCloseCommand(normalizedText);

        return {
          success: true,
          transcription: rawText,
          normalized: normalizedText,
        };
      }

      // Actualizar transcripción actual
      this.currentTranscription = normalizedText;

      // Crear evento de transcripción
      const event: TranscriptionEvent = {
        type: "transcription",
        data: rawText,
        normalized: normalizedText,
        timestamp: new Date(),
      };

      // Notificar a todos los listeners
      this.notifyListeners(event);

      // Ocultar Aura después de un delay
      setTimeout(() => {
        this.mhc.hideAura();
      }, 1000);

      return {
        success: true,
        transcription: rawText,
        normalized: normalizedText,
      };
    } catch (error) {
      // console.error(
      //   "❌ TranscriptionService: Error procesando transcripción:",
      //   error,
      // );

      const errorEvent: TranscriptionEvent = {
        type: "error",
        data: error instanceof Error ? error.message : "Error desconocido",
        timestamp: new Date(),
      };

      this.notifyListeners(errorEvent);

      return {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido",
      };
    }
  }

  private handleCloseCommand(command: string): void {
    // console.log(
    //   `🚪 TranscriptionService: Ejecutando comando de cierre: ${command}`,
    // );

    const commandEvent: TranscriptionEvent = {
      type: "command",
      data: command,
      timestamp: new Date(),
    };

    this.notifyListeners(commandEvent);

    // Cerrar WebView
    this.mhc.closeWebView();
  }

  private notifyListeners(event: TranscriptionEvent): void {
    this.eventListeners.forEach((listener) => {
      try {
        listener(event);
      } catch (error) {
        // console.error("❌ TranscriptionService: Error en listener:", error);
      }
    });
  }

  private handleTranscriptionEvent = (e: Event): void => {
    const customEvent = e as CustomEvent<string>;
    const transcriptionText = customEvent.detail;

    // console.log(
    //   "🎤 TranscriptionService: Evento de transcripción recibido:",
    //   transcriptionText,
    // );
    this.processTranscription(transcriptionText);
  };

  // ========== PUBLIC METHODS ==========
  public startListening(): void {
    if (this.isListening) {
      // console.warn("⚠️ TranscriptionService: Ya está escuchando");
      return;
    }

    // console.log(
    //   "🎤 TranscriptionService: Iniciando escucha de transcripciones",
    // );

    window.addEventListener(
      "transcriptionEvent",
      this.handleTranscriptionEvent,
    );
    this.isListening = true;

    // Notificar a MHC que la página está cargada
    this.mhc.onPageLoaded();
  }

  public stopListening(): void {
    if (!this.isListening) {
      // console.warn("⚠️ TranscriptionService: No está escuchando");
      return;
    }

    // console.log(
    //   "🔇 TranscriptionService: Deteniendo escucha de transcripciones",
    // );

    window.removeEventListener(
      "transcriptionEvent",
      this.handleTranscriptionEvent,
    );
    this.isListening = false;
  }

  public addEventListener(
    listener: (event: TranscriptionEvent) => void,
  ): () => void {
    this.eventListeners.add(listener);
    // console.log(
    //   `📡 TranscriptionService: Listener añadido (total: ${this.eventListeners.size})`,
    // );

    // Retornar función para remover el listener
    return () => {
      this.eventListeners.delete(listener);
      // console.log(
      //   `📡 TranscriptionService: Listener removido (total: ${this.eventListeners.size})`,
      // );
    };
  }

  public simulateTranscription(text: string): void {
    // console.log("🧪 TranscriptionService: Simulando transcripción:", text);

    const customEvent = new CustomEvent("transcriptionEvent", {
      detail: text,
    });

    window.dispatchEvent(customEvent);
  }

  public clearTranscription(): void {
    this.currentTranscription = null;
    // console.log("🧹 TranscriptionService: Transcripción limpiada");
  }

  public reset(): void {
    this.stopListening();
    this.clearTranscription();
    this.eventListeners.clear();
    // console.log("🔄 TranscriptionService: Servicio reseteado");
  }

  // ========== MHC PROXY METHODS ==========
  public setSucwTimeout(timeMS: number): void {
    this.mhc.setSucwTimeout(timeMS);
  }

  public closeWebView(): void {
    this.mhc.closeWebView();
  }

  public hideAura(): void {
    this.mhc.hideAura();
  }

  public speakAura(text: string): void {
    this.mhc.speakAura(text);
  }

  public sendAura(text: string): void {
    this.mhc.sendAura(text);
  }

  public getId(): string {
    return this.mhc.getId();
  }

  // ========== UTILITY METHODS ==========
  public isMHCAvailable(): boolean {
    return this.mhc instanceof MHC && (this.mhc as MHC).isAvailable();
  }

  public getStatus(): object {
    return {
      listening: this.isListening,
      currentTranscription: this.currentTranscription,
      listenersCount: this.eventListeners.size,
      mhcAvailable: this.isMHCAvailable(),
      mhcStatus: this.mhc instanceof MHC ? (this.mhc as MHC).getStatus() : null,
    };
  }

  public getCloseCommands(): string[] {
    return [...this.CLOSE_COMMANDS];
  }

  public addCloseCommand(command: string): void {
    const normalizedCommand = command.toLowerCase().trim();
    if (!this.CLOSE_COMMANDS.includes(normalizedCommand)) {
      this.CLOSE_COMMANDS.push(normalizedCommand);
      // console.log(
      //   `✅ TranscriptionService: Comando de cierre añadido: ${normalizedCommand}`,
      // );
    }
  }

  public async testNormalization(text: string): Promise<string> {
    return await normalize(text);
  }

  // ========== CLEANUP ==========
  public destroy(): void {
    this.reset();

    try {
      // @ts-ignore
      delete window.testTranscription;
      // @ts-ignore
      delete window.transcriptionService;
    } catch (error) {
      // console.warn(
      //   "⚠️ TranscriptionService: No se pudieron limpiar variables globales:",
      //   error,
      // );
    }

    // console.log("💀 TranscriptionService: Servicio destruido");
  }
}

// ========== SINGLETON EXPORT ==========
export const transcriptionService = TranscriptionService.getInstance();
