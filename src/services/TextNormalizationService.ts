// services/TextNormalizationService.ts
/**
 * SERVICIO ÚNICO DE NORMALIZACIÓN DE TEXTO
 * 
 * Centraliza toda la lógica de normalización de texto para eliminar duplicación
 * entre TranscriptionService y _helpersService
 */

// ========== TYPES ==========
export interface NormalizationOptions {
  removeAccents?: boolean;
  toLowerCase?: boolean;
  trimWhitespace?: boolean;
  removeSpecialChars?: boolean;
  maxLength?: number;
  preserveSpaces?: boolean;
}

export interface SanitizationOptions {
  maxLength?: number;
  removeHtmlChars?: boolean;
  removeScriptChars?: boolean;
  preserveBasicPunctuation?: boolean;
}

// ========== SINGLETON SERVICE ==========
class TextNormalizationService {
  private static instance: TextNormalizationService;

  // Mapeo de acentos y caracteres especiales
  private readonly accentMap = new Map([
    ['á', 'a'], ['à', 'a'], ['ä', 'a'], ['â', 'a'], ['ā', 'a'], ['ã', 'a'],
    ['é', 'e'], ['è', 'e'], ['ë', 'e'], ['ê', 'e'], ['ē', 'e'],
    ['í', 'i'], ['ì', 'i'], ['ï', 'i'], ['î', 'i'], ['ī', 'i'],
    ['ó', 'o'], ['ò', 'o'], ['ö', 'o'], ['ô', 'o'], ['ō', 'o'], ['õ', 'o'],
    ['ú', 'u'], ['ù', 'u'], ['ü', 'u'], ['û', 'u'], ['ū', 'u'],
    ['ñ', 'n'], ['ç', 'c'],
    ['Á', 'A'], ['À', 'A'], ['Ä', 'A'], ['Â', 'A'], ['Ā', 'A'], ['Ã', 'A'],
    ['É', 'E'], ['È', 'E'], ['Ë', 'E'], ['Ê', 'E'], ['Ē', 'E'],
    ['Í', 'I'], ['Ì', 'I'], ['Ï', 'I'], ['Î', 'I'], ['Ī', 'I'],
    ['Ó', 'O'], ['Ò', 'O'], ['Ö', 'O'], ['Ô', 'O'], ['Ō', 'O'], ['Õ', 'O'],
    ['Ú', 'U'], ['Ù', 'U'], ['Ü', 'U'], ['Û', 'U'], ['Ū', 'U'],
    ['Ñ', 'N'], ['Ç', 'C']
  ]);

  // Caracteres HTML peligrosos
  private readonly htmlChars = /[<>\"'&]/g;
  
  // Caracteres de script peligrosos
  private readonly scriptChars = /[<>\"'&{}()[\]]/g;

  private constructor() {}

  public static getInstance(): TextNormalizationService {
    if (!TextNormalizationService.instance) {
      TextNormalizationService.instance = new TextNormalizationService();
    }
    return TextNormalizationService.instance;
  }

  // ========== NORMALIZACIÓN PRINCIPAL ==========
  /**
   * Normaliza texto según las opciones especificadas
   * @param text Texto a normalizar
   * @param options Opciones de normalización
   * @returns Texto normalizado
   */
  public normalize(text: string, options: NormalizationOptions = {}): string {
    if (!text) return "";

    const {
      removeAccents = true,
      toLowerCase = true,
      trimWhitespace = true,
      removeSpecialChars = false,
      maxLength,
      preserveSpaces = true
    } = options;

    let result = text;

    // 1. Convertir a minúsculas
    if (toLowerCase) {
      result = result.toLowerCase();
    }

    // 2. Remover acentos
    if (removeAccents) {
      result = this.removeAccents(result);
    }

    // 3. Limpiar espacios
    if (trimWhitespace) {
      if (preserveSpaces) {
        // Normalizar espacios múltiples a uno solo
        result = result.replace(/\s+/g, " ").trim();
      } else {
        // Remover todos los espacios
        result = result.replace(/\s/g, "");
      }
    }

    // 4. Remover caracteres especiales
    if (removeSpecialChars) {
      result = result.replace(/[^\w\s]/g, "");
    }

    // 5. Limitar longitud
    if (maxLength && result.length > maxLength) {
      result = result.substring(0, maxLength);
    }

    return result;
  }

  /**
   * Normalización asíncrona (compatible con TranscriptionService)
   * @param text Texto a normalizar
   * @param options Opciones de normalización
   * @returns Promise con texto normalizado
   */
  public async normalizeAsync(text: string, options: NormalizationOptions = {}): Promise<string> {
    return Promise.resolve(this.normalize(text, options));
  }

  // ========== SANITIZACIÓN ==========
  /**
   * Sanitiza texto para uso seguro en APIs
   * @param text Texto a sanitizar
   * @param options Opciones de sanitización
   * @returns Texto sanitizado
   */
  public sanitize(text: string, options: SanitizationOptions = {}): string {
    if (!text) return "";

    const {
      maxLength = 1000,
      removeHtmlChars = true,
      removeScriptChars = false,
      preserveBasicPunctuation = true
    } = options;

    let result = text;

    // 1. Remover caracteres HTML peligrosos
    if (removeHtmlChars) {
      result = result.replace(this.htmlChars, "");
    }

    // 2. Remover caracteres de script peligrosos
    if (removeScriptChars) {
      result = result.replace(this.scriptChars, "");
    }

    // 3. Preservar puntuación básica si se especifica
    if (!preserveBasicPunctuation) {
      result = result.replace(/[^\w\s]/g, "");
    }

    // 4. Limitar longitud y limpiar
    result = result.substring(0, maxLength).trim();

    return result;
  }

  // ========== UTILIDADES ESPECÍFICAS ==========
  /**
   * Remueve acentos de un texto
   * @param text Texto con acentos
   * @returns Texto sin acentos
   */
  public removeAccents(text: string): string {
    let result = text;
    for (const [accented, plain] of this.accentMap.entries()) {
      const regex = new RegExp(accented, 'g');
      result = result.replace(regex, plain);
    }
    return result;
  }

  /**
   * Normalización específica para transcripciones de voz
   * @param text Texto transcrito
   * @returns Texto normalizado para procesamiento de voz
   */
  public normalizeForSpeech(text: string): string {
    return this.normalize(text, {
      removeAccents: true,
      toLowerCase: true,
      trimWhitespace: true,
      removeSpecialChars: false,
      preserveSpaces: true
    });
  }

  /**
   * Normalización específica para validación de respuestas de juego
   * @param text Respuesta del usuario
   * @returns Texto normalizado para validación
   */
  public normalizeForGameResponse(text: string): string {
    return this.normalize(text, {
      removeAccents: true,
      toLowerCase: true,
      trimWhitespace: true,
      removeSpecialChars: false,
      preserveSpaces: true,
      maxLength: 200
    });
  }

  /**
   * Sanitización para entrada de usuario en APIs
   * @param text Entrada del usuario
   * @returns Texto sanitizado para API
   */
  public sanitizeForAPI(text: string): string {
    return this.sanitize(text, {
      maxLength: 1000,
      removeHtmlChars: true,
      removeScriptChars: true,
      preserveBasicPunctuation: true
    });
  }

  // ========== VALIDACIONES ==========
  /**
   * Verifica si un texto está vacío después de normalización
   * @param text Texto a verificar
   * @returns true si está vacío después de normalización
   */
  public isEmpty(text: string): boolean {
    return this.normalize(text).length === 0;
  }

  /**
   * Verifica si un texto es válido para procesamiento
   * @param text Texto a verificar
   * @param minLength Longitud mínima requerida
   * @param maxLength Longitud máxima permitida
   * @returns true si es válido
   */
  public isValid(text: string, minLength: number = 1, maxLength: number = 1000): boolean {
    if (!text) return false;
    
    const normalized = this.normalize(text);
    return normalized.length >= minLength && normalized.length <= maxLength;
  }

  // ========== COMPARACIONES ==========
  /**
   * Compara dos textos después de normalización
   * @param text1 Primer texto
   * @param text2 Segundo texto
   * @param options Opciones de normalización
   * @returns true si son iguales después de normalización
   */
  public areEqual(text1: string, text2: string, options: NormalizationOptions = {}): boolean {
    const normalized1 = this.normalize(text1, options);
    const normalized2 = this.normalize(text2, options);
    return normalized1 === normalized2;
  }

  /**
   * Verifica si un texto contiene otro después de normalización
   * @param text Texto principal
   * @param searchText Texto a buscar
   * @param options Opciones de normalización
   * @returns true si contiene el texto
   */
  public contains(text: string, searchText: string, options: NormalizationOptions = {}): boolean {
    const normalizedText = this.normalize(text, options);
    const normalizedSearch = this.normalize(searchText, options);
    return normalizedText.includes(normalizedSearch);
  }
}

// ========== EXPORT SINGLETON INSTANCE ==========
export const textNormalizer = TextNormalizationService.getInstance();

// ========== LEGACY COMPATIBILITY ==========
/**
 * Función de compatibilidad con TranscriptionService
 * @deprecated Usar textNormalizer.normalizeAsync() en su lugar
 */
export const normalize = (str: string): Promise<string> => {
  return textNormalizer.normalizeAsync(str);
};

/**
 * Función de compatibilidad con _helpersService
 * @deprecated Usar textNormalizer.sanitizeForAPI() en su lugar
 */
export const sanitizeText = (text: string, maxLength: number = 1000): string => {
  return textNormalizer.sanitize(text, { maxLength });
};
