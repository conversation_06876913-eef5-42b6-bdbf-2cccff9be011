// hooks/useSpeechTesting.ts
import { useCallback, useRef, useState } from 'react';
import { type GameResponseType } from './useGameResponseValidator';

// ========== TYPES ==========
export interface TestScenario {
  id: string;
  name: string;
  description: string;
  inputs: string[];
  expectedOutputs: GameResponseType[];
  timeout?: number;
}

export interface TestResult {
  scenarioId: string;
  scenarioName: string;
  totalTests: number;
  passed: number;
  failed: number;
  results: Array<{
    input: string;
    expected: GameResponseType;
    actual: GameResponseType;
    passed: boolean;
    confidence: number;
    duration: number;
  }>;
  duration: number;
  passRate: number;
}

export interface SpeechSimulator {
  // Basic simulation
  simulateInput: (text: string, delay?: number) => Promise<void>;
  simulateSequence: (texts: string[], delay?: number) => Promise<void>;

  // Advanced simulation
  simulateWithNoise: (text: string, noiseLevel?: number) => Promise<void>;
  simulateWithAccent: (text: string, accent?: 'andaluz' | 'mexicano' | 'argentino') => Promise<void>;
  simulateInterruption: (text: string, interruptAt?: number) => Promise<void>;

  // Realistic scenarios
  simulateHesitation: (text: string) => Promise<void>;
  simulateBackground: (text: string, background?: 'music' | 'traffic' | 'crowd') => Promise<void>;
  simulateVolume: (text: string, volume?: 'whisper' | 'normal' | 'loud') => Promise<void>;
}

// ========== PREDEFINED TEST SCENARIOS ==========
const DEFAULT_SCENARIOS: TestScenario[] = [
  {
    id: 'basic-responses',
    name: 'Respuestas Básicas',
    description: 'Prueba las respuestas fundamentales del juego',
    inputs: ['sí', 'no', 'tal vez', 'no lo sé'],
    expectedOutputs: ['yes', 'no', 'maybe', 'unknown']
  },
  {
    id: 'variations',
    name: 'Variaciones Comunes',
    description: 'Prueba sinónimos y variaciones',
    inputs: ['claro', 'para nada', 'quizás', 'ni idea'],
    expectedOutputs: ['yes', 'no', 'maybe', 'unknown']
  },
  {
    id: 'colloquial',
    name: 'Expresiones Coloquiales',
    description: 'Prueba lenguaje informal y coloquial',
    inputs: ['vale', 'nanai', 'meh', 'ni puta idea'],
    expectedOutputs: ['yes', 'no', 'maybe', 'unknown']
  },
  {
    id: 'long-phrases',
    name: 'Frases Largas',
    description: 'Prueba respuestas en contexto de frases',
    inputs: [
      'bueno sí creo que sí',
      'no no para nada',
      'pues no sé tal vez',
      'la verdad es que no tengo ni idea'
    ],
    expectedOutputs: ['yes', 'no', 'maybe', 'unknown']
  },
  {
    id: 'edge-cases',
    name: 'Casos Límite',
    description: 'Prueba casos ambiguos y difíciles',
    inputs: ['bueno...', 'ehh', 'mmm', 'pues'],
    expectedOutputs: ['maybe', 'unknown', 'maybe', 'maybe']
  },
  {
    id: 'noise-simulation',
    name: 'Simulación de Ruido',
    description: 'Prueba con texto que simula interferencias',
    inputs: ['sí... *ruido*', 'no eh... perdón', 'tal... vez?', 'no... no sé'],
    expectedOutputs: ['yes', 'no', 'maybe', 'unknown']
  }
];

// ========== ACCENT TRANSFORMATIONS ==========
const ACCENT_TRANSFORMATIONS = {
  andaluz: {
    's': 'h',
    'z': 's',
    'c': 's',
    // Ceceo/seseo
    'gracias': 'grasiah',
    'hacer': 'hasé',
  },
  mexicano: {
    // Acentuación mexicana
    'sí': 'síí',
    'también': 'tambiéen',
    'está': 'estáá',
  },
  argentino: {
    'll': 'sh',
    'y': 'sh',
    // Voseo
    'tienes': 'tenés',
    'eres': 'sos',
  }
};

// ========== NOISE SIMULATION ==========
const NOISE_PATTERNS = {
  music: ['*música*', '*sonido*', '*beat*'],
  traffic: ['*coches*', '*claxon*', '*motor*'],
  crowd: ['*gente*', '*murmullo*', '*voces*']
};

// ========== CUSTOM HOOK ==========
export const useSpeechTesting = (
  simulateTranscription: (text: string) => void,
  validateResponse: (text: string) => { type: GameResponseType; confidence: number }
) => {
  const [isRunning, setIsRunning] = useState(false);
  const [currentTest, setCurrentTest] = useState<string | null>(null);
  const [results, setResults] = useState<TestResult[]>([]);
  const abortControllerRef = useRef<AbortController | null>(null);

  // ========== BASIC SIMULATION ==========
  const simulateInput = useCallback(async (text: string, delay = 500): Promise<void> => {
    return new Promise((resolve) => {
      setTimeout(() => {
        simulateTranscription(text);
        resolve();
      }, delay);
    });
  }, [simulateTranscription]);

  const simulateSequence = useCallback(async (texts: string[], delay = 1000): Promise<void> => {
    for (const text of texts) {
      if (abortControllerRef.current?.signal.aborted) break;
      await simulateInput(text, delay);
    }
  }, [simulateInput]);

  // ========== ADVANCED SIMULATION ==========
  const simulateWithNoise = useCallback(async (text: string, noiseLevel = 0.3): Promise<void> => {
    let noisyText = text;

    if (Math.random() < noiseLevel) {
      const noiseTypes = ['*ruido*', '*interferencia*', '*eco*'];
      const noise = noiseTypes[Math.floor(Math.random() * noiseTypes.length)];
      noisyText = Math.random() < 0.5 ? `${noise} ${text}` : `${text} ${noise}`;
    }

    await simulateInput(noisyText);
  }, [simulateInput]);

  const simulateWithAccent = useCallback(async (
    text: string,
    accent: 'andaluz' | 'mexicano' | 'argentino' = 'andaluz'
  ): Promise<void> => {
    let accentedText = text;
    const transformations = ACCENT_TRANSFORMATIONS[accent];

    for (const [from, to] of Object.entries(transformations)) {
      accentedText = accentedText.replace(new RegExp(from, 'gi'), to);
    }

    await simulateInput(accentedText);
  }, [simulateInput]);

  const simulateInterruption = useCallback(async (text: string, interruptAt = 0.5): Promise<void> => {
    const cutPoint = Math.floor(text.length * interruptAt);
    const interrupted = text.substring(0, cutPoint) + '... *interrupción*';

    await simulateInput(interrupted, 300);
    // Simular que el usuario repite completo
    await simulateInput(text, 800);
  }, [simulateInput]);

  const simulateHesitation = useCallback(async (text: string): Promise<void> => {
    const hesitations = ['ehh...', 'mmm...', 'pues...', 'bueno...'];
    const hesitation = hesitations[Math.floor(Math.random() * hesitations.length)];

    await simulateInput(hesitation, 400);
    await simulateInput(text, 600);
  }, [simulateInput]);

  const simulateBackground = useCallback(async (
    text: string,
    background: 'music' | 'traffic' | 'crowd' = 'music'
  ): Promise<void> => {
    const noises = NOISE_PATTERNS[background];
    const noise = noises[Math.floor(Math.random() * noises.length)];
    const noisyText = `${noise} ${text}`;

    await simulateInput(noisyText);
  }, [simulateInput]);

  const simulateVolume = useCallback(async (
    text: string,
    volume: 'whisper' | 'normal' | 'loud' = 'normal'
  ): Promise<void> => {
    let volumeText = text;

    switch (volume) {
      case 'whisper':
        volumeText = `*susurro* ${text.toLowerCase()}`;
        break;
      case 'loud':
        volumeText = `*GRITO* ${text.toUpperCase()}`;
        break;
      default:
        volumeText = text;
    }

    await simulateInput(volumeText);
  }, [simulateInput]);

  // ========== TEST EXECUTION ==========
  const runTestScenario = useCallback(async (scenario: TestScenario): Promise<TestResult> => {
    const startTime = Date.now();
    setCurrentTest(scenario.name);

    const testResults = [];

    for (let i = 0; i < scenario.inputs.length; i++) {
      if (abortControllerRef.current?.signal.aborted) break;

      const input = scenario.inputs[i];
      const expected = scenario.expectedOutputs[i];
      const testStart = Date.now();

      // Simular la entrada
      await simulateInput(input, 200);

      // Validar la respuesta
      const validation = validateResponse(input);
      const testDuration = Date.now() - testStart;

      testResults.push({
        input,
        expected,
        actual: validation.type,
        passed: validation.type === expected,
        confidence: validation.confidence,
        duration: testDuration
      });
    }

    const totalDuration = Date.now() - startTime;
    const passed = testResults.filter(r => r.passed).length;
    const failed = testResults.length - passed;
    const passRate = testResults.length > 0 ? (passed / testResults.length) * 100 : 0;

    const result: TestResult = {
      scenarioId: scenario.id,
      scenarioName: scenario.name,
      totalTests: testResults.length,
      passed,
      failed,
      results: testResults,
      duration: totalDuration,
      passRate
    };

    setCurrentTest(null);
    return result;
  }, [simulateInput, validateResponse]);

  const runAllScenarios = useCallback(async (scenarios = DEFAULT_SCENARIOS): Promise<TestResult[]> => {
    setIsRunning(true);
    setResults([]);
    abortControllerRef.current = new AbortController();

    const allResults: TestResult[] = [];

    try {
      for (const scenario of scenarios) {
        if (abortControllerRef.current.signal.aborted) break;

        const result = await runTestScenario(scenario);
        allResults.push(result);
        setResults(prev => [...prev, result]);

        // Pausa entre escenarios
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    } catch (error) {
      console.error('Error running test scenarios:', error);
    } finally {
      setIsRunning(false);
      setCurrentTest(null);
    }

    return allResults;
  }, [runTestScenario]);

  const stopTesting = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    setIsRunning(false);
    setCurrentTest(null);
  }, []);

  // ========== STRESS TESTING ==========
  const runStressTest = useCallback(async (
    duration = 60000, // 1 minuto
    frequency = 2000   // cada 2 segundos
  ): Promise<{ totalInputs: number; successful: number; failed: number; avgResponseTime: number }> => {
    setIsRunning(true);
    const startTime = Date.now();
    const responses = ['sí', 'no', 'tal vez', 'no lo sé'];
    const results = { total: 0, successful: 0, failed: 0, totalTime: 0 };

    abortControllerRef.current = new AbortController();

    while (Date.now() - startTime < duration && !abortControllerRef.current.signal.aborted) {
      const randomResponse = responses[Math.floor(Math.random() * responses.length)];
      const testStart = Date.now();

      try {
        await simulateInput(randomResponse, 100);
        const validation = validateResponse(randomResponse);
        const responseTime = Date.now() - testStart;

        results.total++;
        results.totalTime += responseTime;

        if (validation.type !== 'invalid' && validation.confidence > 0.6) {
          results.successful++;
        } else {
          results.failed++;
        }
      } catch (error) {
        results.failed++;
      }

      await new Promise(resolve => setTimeout(resolve, frequency));
    }

    setIsRunning(false);

    return {
      totalInputs: results.total,
      successful: results.successful,
      failed: results.failed,
      avgResponseTime: results.total > 0 ? results.totalTime / results.total : 0
    };
  }, [simulateInput, validateResponse]);

  // ========== SPEECH SIMULATOR OBJECT ==========
  const speechSimulator: SpeechSimulator = {
    simulateInput,
    simulateSequence,
    simulateWithNoise,
    simulateWithAccent,
    simulateInterruption,
    simulateHesitation,
    simulateBackground,
    simulateVolume
  };

  // ========== REPORT GENERATION ==========
  const generateReport = useCallback((testResults: TestResult[]) => {
    const totalTests = testResults.reduce((sum, r) => sum + r.totalTests, 0);
    const totalPassed = testResults.reduce((sum, r) => sum + r.passed, 0);
    const totalFailed = testResults.reduce((sum, r) => sum + r.failed, 0);
    const overallPassRate = totalTests > 0 ? (totalPassed / totalTests) * 100 : 0;
    const avgDuration = testResults.length > 0
      ? testResults.reduce((sum, r) => sum + r.duration, 0) / testResults.length
      : 0;

    return {
      summary: {
        totalScenarios: testResults.length,
        totalTests,
        totalPassed,
        totalFailed,
        overallPassRate,
        avgDuration
      },
      scenarios: testResults,
      recommendations: generateRecommendations(testResults)
    };
  }, []);

  const generateRecommendations = useCallback((testResults: TestResult[]) => {
    const recommendations = [];

    // Analizar patrones de fallo
    const failedTests = testResults.flatMap(r => r.results.filter(t => !t.passed));
    const failurePatterns = new Map<string, number>();

    failedTests.forEach(test => {
      const key = `${test.expected}_as_${test.actual}`;
      failurePatterns.set(key, (failurePatterns.get(key) || 0) + 1);
    });

    // Generar recomendaciones basadas en patrones
    if (failurePatterns.size > 0) {
      recommendations.push("Patrones de error detectados:");
      failurePatterns.forEach((count, pattern) => {
        recommendations.push(`• ${pattern}: ${count} casos`);
      });
    }

    // Recomendaciones por tasa de éxito
    const lowPerformanceScenarios = testResults.filter(r => r.passRate < 80);
    if (lowPerformanceScenarios.length > 0) {
      recommendations.push("\nEscenarios que necesitan mejora:");
      lowPerformanceScenarios.forEach(scenario => {
        recommendations.push(`• ${scenario.scenarioName}: ${scenario.passRate.toFixed(1)}% éxito`);
      });
    }

    return recommendations;
  }, []);

  return {
    // State
    isRunning,
    currentTest,
    results,

    // Testing execution
    runTestScenario,
    runAllScenarios,
    runStressTest,
    stopTesting,

    // Speech simulator
    speechSimulator,

    // Reporting
    generateReport,

    // Predefined scenarios
    defaultScenarios: DEFAULT_SCENARIOS,

    // Utilities
    clearResults: () => setResults([]),
  };
};
